<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    <button onclick="sendPing()">Send Ping</button>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function connect() {
            const userId = 'test_' + Math.random().toString(36).substr(2, 9);
            const wsUrl = `ws://localhost:8000/api/v1/ws/${userId}`;
            
            console.log('Connecting to:', wsUrl);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket connected');
                statusDiv.textContent = 'Connected';
                statusDiv.style.color = 'green';
                addMessage('Connected to WebSocket');
            };
            
            ws.onmessage = function(event) {
                console.log('Message received:', event.data);
                addMessage('Received: ' + event.data);
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket disconnected:', event.code, event.reason);
                statusDiv.textContent = 'Disconnected';
                statusDiv.style.color = 'red';
                addMessage('Disconnected: ' + event.code + ' - ' + event.reason);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                addMessage('Error: ' + error);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
            }
        }
        
        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'ping',
                    timestamp: new Date().toISOString()
                };
                ws.send(JSON.stringify(message));
                addMessage('Sent ping');
            } else {
                addMessage('WebSocket not connected');
            }
        }
        
        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            messagesDiv.appendChild(div);
        }
    </script>
</body>
</html>