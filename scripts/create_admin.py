#!/usr/bin/env python3
"""
创建默认管理员用户脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.database import Base
from app.models.user import User
from app.api.v1.auth import hash_password
import uuid
from datetime import datetime

def create_admin_user(database_url: str = None):
    """创建默认管理员用户"""
    if database_url is None:
        database_url = os.getenv("DATABASE_URL", "sqlite:///./data/loop_hole.db")
    
    print(f"连接数据库: {database_url}")
    
    # 创建数据库引擎
    if "sqlite" in database_url:
        # 确保数据目录存在
        db_path = database_url.replace("sqlite:///", "")
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        engine = create_engine(
            database_url,
            connect_args={"check_same_thread": False}
        )
    else:
        engine = create_engine(database_url)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查是否已存在管理员用户
        existing_admin = db.query(User).filter(User.username == "admin").first()
        
        if existing_admin:
            print("❌ 管理员用户已存在")
            print(f"用户名: {existing_admin.username}")
            print(f"邮箱: {existing_admin.email}")
            print("如需重置密码，请删除现有用户后重新运行此脚本")
            return False
        
        # 创建管理员用户
        admin_password = "admin123"  # 默认密码
        hashed_password = hash_password(admin_password)
        
        admin_user = User(
            id=str(uuid.uuid4()),
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="系统管理员",
            role="admin",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ 管理员用户创建成功！")
        print(f"用户名: {admin_user.username}")
        print(f"密码: {admin_password}")
        print(f"邮箱: {admin_user.email}")
        print(f"角色: {admin_user.role}")
        print("\n⚠️  请在首次登录后修改默认密码！")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

def create_demo_user():
    """创建演示用户"""
    database_url = os.getenv("DATABASE_URL", "sqlite:///./data/loop_hole.db")
    
    if "sqlite" in database_url:
        engine = create_engine(
            database_url,
            connect_args={"check_same_thread": False}
        )
    else:
        engine = create_engine(database_url)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查是否已存在演示用户
        existing_demo = db.query(User).filter(User.username == "demo").first()
        
        if existing_demo:
            print("演示用户已存在")
            return False
        
        # 创建演示用户
        demo_password = "demo123"
        hashed_password = hash_password(demo_password)
        
        demo_user = User(
            id=str(uuid.uuid4()),
            username="demo",
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="演示用户",
            role="user",
            is_active=True
        )
        
        db.add(demo_user)
        db.commit()
        db.refresh(demo_user)
        
        print("✅ 演示用户创建成功！")
        print(f"用户名: {demo_user.username}")
        print(f"密码: {demo_password}")
        print(f"邮箱: {demo_user.email}")
        print(f"角色: {demo_user.role}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建演示用户失败: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    print("Loop Hole - 用户管理脚本")
    print("=" * 40)
    
    # 创建管理员用户
    print("\n1. 创建管理员用户...")
    create_admin_user()
    
    # 创建演示用户
    print("\n2. 创建演示用户...")
    create_demo_user()
    
    print("\n" + "=" * 40)
    print("用户创建完成！")
    print("\n登录信息:")
    print("管理员 - 用户名: admin, 密码: admin123")
    print("演示用户 - 用户名: demo, 密码: demo123")
    print("\n访问地址: http://localhost:3000")