#!/bin/bash

# Loop Hole 系统健康检查脚本
# 快速验证系统是否正常运行

set -e  # 遇到错误时不退出，继续检查其他项

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 状态计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    ((WARNING_CHECKS++))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((FAILED_CHECKS++))
}

log_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

# 增加检查计数
inc_check() {
    ((TOTAL_CHECKS++))
}

# 检查命令是否存在
check_command() {
    if command -v $1 &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查URL是否可访问
check_url() {
    local url=$1
    local timeout=${2:-10}

    if curl -s -f -m $timeout "$url" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查Docker环境
check_docker() {
    log_header "Docker 环境检查"

    inc_check
    if check_command docker; then
        log_success "Docker 已安装: $(docker --version | head -n1)"
    else
        log_error "Docker 未安装或不在PATH中"
        return
    fi

    inc_check
    if check_command docker-compose || docker compose version > /dev/null 2>&1; then
        if docker compose version > /dev/null 2>&1; then
            DOCKER_COMPOSE="docker compose"
            log_success "Docker Compose 已安装: $(docker compose version --short)"
        else
            DOCKER_COMPOSE="docker-compose"
            log_success "Docker Compose 已安装: $(docker-compose --version | head -n1)"
        fi
    else
        log_error "Docker Compose 未安装"
        return
    fi

    inc_check
    if docker info > /dev/null 2>&1; then
        log_success "Docker daemon 运行正常"
    else
        log_error "Docker daemon 未运行或权限不足"
    fi
}

# 检查容器状态
check_containers() {
    log_header "容器状态检查"

    if [ ! -f docker-compose.dev.yml ]; then
        log_error "docker-compose.dev.yml 文件不存在"
        return
    fi

    # 检查各个服务容器
    local services=("postgres" "redis" "web" "worker")

    for service in "${services[@]}"; do
        inc_check
        local container_status=$($DOCKER_COMPOSE -f docker-compose.dev.yml ps -q $service 2>/dev/null)

        if [ -n "$container_status" ]; then
            local is_running=$(docker inspect -f '{{.State.Running}}' $container_status 2>/dev/null)
            if [ "$is_running" = "true" ]; then
                local uptime=$(docker inspect -f '{{.State.StartedAt}}' $container_status 2>/dev/null)
                log_success "$service 容器运行正常 (启动时间: $uptime)"
            else
                log_error "$service 容器已停止"
            fi
        else
            log_error "$service 容器不存在"
        fi
    done

    # 检查前端容器（可能不存在）
    inc_check
    local frontend_status=$($DOCKER_COMPOSE -f docker-compose.dev.yml ps -q frontend 2>/dev/null)
    if [ -n "$frontend_status" ]; then
        local is_running=$(docker inspect -f '{{.State.Running}}' $frontend_status 2>/dev/null)
        if [ "$is_running" = "true" ]; then
            log_success "frontend 容器运行正常"
        else
            log_warning "frontend 容器已停止"
        fi
    else
        log_warning "frontend 容器不存在 (可能使用外部前端服务)"
    fi
}

# 检查网络连接
check_network() {
    log_header "网络连接检查"

    inc_check
    if check_url "http://localhost:8000/api/v1/health" 5; then
        log_success "后端API可访问: http://localhost:8000"
    else
        log_error "后端API不可访问: http://localhost:8000"
    fi

    inc_check
    if check_url "http://localhost" 5; then
        log_success "前端界面可访问: http://localhost"
    else
        log_warning "前端界面不可访问: http://localhost (可能使用其他端口)"
    fi

    inc_check
    if check_url "http://localhost:8000/docs" 5; then
        log_success "API文档可访问: http://localhost:8000/docs"
    else
        log_error "API文档不可访问"
    fi
}

# 检查数据库连接
check_database() {
    log_header "数据库连接检查"

    inc_check
    if $DOCKER_COMPOSE -f docker-compose.dev.yml exec postgres pg_isready -U loop_hole > /dev/null 2>&1; then
        log_success "PostgreSQL 连接正常"
    else
        log_error "PostgreSQL 连接失败"
        return
    fi

    inc_check
    local db_version=$($DOCKER_COMPOSE -f docker-compose.dev.yml exec postgres psql -U loop_hole -d loop_hole -t -c "SELECT version();" 2>/dev/null | head -n1 | xargs)
    if [ -n "$db_version" ]; then
        log_success "数据库查询正常"
        log_info "数据库版本: ${db_version:0:50}..."
    else
        log_error "数据库查询失败"
    fi

    inc_check
    local table_count=$($DOCKER_COMPOSE -f docker-compose.dev.yml exec postgres psql -U loop_hole -d loop_hole -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';" 2>/dev/null | xargs)
    if [ -n "$table_count" ] && [ "$table_count" -gt 0 ]; then
        log_success "数据库表正常 (共 $table_count 个表)"
    else
        log_warning "数据库表可能未初始化"
    fi
}

# 检查Redis连接
check_redis() {
    log_header "Redis 缓存检查"

    inc_check
    if $DOCKER_COMPOSE -f docker-compose.dev.yml exec redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        return
    fi

    inc_check
    local redis_info=$($DOCKER_COMPOSE -f docker-compose.dev.yml exec redis redis-cli info server 2>/dev/null | grep redis_version | cut -d: -f2 | tr -d '\r')
    if [ -n "$redis_info" ]; then
        log_success "Redis 版本: $redis_info"
    else
        log_warning "无法获取Redis版本信息"
    fi

    inc_check
    local used_memory=$($DOCKER_COMPOSE -f docker-compose.dev.yml exec redis redis-cli info memory 2>/dev/null | grep used_memory_human | cut -d: -f2 | tr -d '\r')
    if [ -n "$used_memory" ]; then
        log_info "Redis 内存使用: $used_memory"
        ((PASSED_CHECKS++))
    else
        log_warning "无法获取Redis内存使用信息"
    fi
}

# 检查API功能
check_api() {
    log_header "API 功能检查"

    inc_check
    local health_response=$(curl -s -f http://localhost:8000/api/v1/health 2>/dev/null)
    if [ $? -eq 0 ] && [[ $health_response == *"healthy"* ]]; then
        log_success "健康检查API响应正常"
        log_info "响应: $health_response"
    else
        log_error "健康检查API响应异常"
    fi

    inc_check
    if check_url "http://localhost:8000/api/v1/tasks" 10; then
        log_success "任务管理API可访问"
    else
        log_warning "任务管理API不可访问 (可能需要认证)"
    fi

    inc_check
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:8000/api/v1/health 2>/dev/null)
    if [ $? -eq 0 ]; then
        # 将响应时间转换为毫秒
        response_ms=$(echo "$response_time * 1000" | bc 2>/dev/null || echo "N/A")
        if [[ $response_ms != "N/A" ]] && (( $(echo "$response_time < 2.0" | bc -l) )); then
            log_success "API响应时间: ${response_ms}ms (良好)"
        elif [[ $response_ms != "N/A" ]] && (( $(echo "$response_time < 5.0" | bc -l) )); then
            log_warning "API响应时间: ${response_ms}ms (较慢)"
        else
            log_error "API响应时间: ${response_ms}ms (过慢)"
        fi
    else
        log_error "无法测试API响应时间"
    fi
}

# 检查系统资源
check_resources() {
    log_header "系统资源检查"

    inc_check
    log_info "Docker容器资源使用情况:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null || log_error "无法获取容器资源信息"
    ((PASSED_CHECKS++))

    inc_check
    local disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        log_success "磁盘空间充足 (使用率: ${disk_usage}%)"
    elif [ "$disk_usage" -lt 90 ]; then
        log_warning "磁盘空间紧张 (使用率: ${disk_usage}%)"
    else
        log_error "磁盘空间不足 (使用率: ${disk_usage}%)"
    fi

    inc_check
    if check_command free; then
        local mem_usage=$(free | awk '/^Mem/ {printf "%.1f", $3/$2 * 100}')
        if (( $(echo "$mem_usage < 80" | bc -l) )); then
            log_success "内存使用正常 (${mem_usage}%)"
        elif (( $(echo "$mem_usage < 90" | bc -l) )); then
            log_warning "内存使用较高 (${mem_usage}%)"
        else
            log_error "内存使用过高 (${mem_usage}%)"
        fi
    else
        log_info "无法检查内存使用情况 (free命令不可用)"
        ((PASSED_CHECKS++))
    fi
}

# 检查日志错误
check_logs() {
    log_header "日志错误检查"

    inc_check
    local error_count=$($DOCKER_COMPOSE -f docker-compose.dev.yml logs --tail=100 2>/dev/null | grep -i error | wc -l)
    if [ "$error_count" -eq 0 ]; then
        log_success "最近日志中无错误记录"
    elif [ "$error_count" -lt 5 ]; then
        log_warning "最近日志中发现 $error_count 个错误"
    else
        log_error "最近日志中发现 $error_count 个错误 (较多)"
    fi

    inc_check
    local warning_count=$($DOCKER_COMPOSE -f docker-compose.dev.yml logs --tail=100 2>/dev/null | grep -i warning | wc -l)
    if [ "$warning_count" -eq 0 ]; then
        log_success "最近日志中无警告记录"
    elif [ "$warning_count" -lt 10 ]; then
        log_info "最近日志中发现 $warning_count 个警告 (正常范围)"
        ((PASSED_CHECKS++))
    else
        log_warning "最近日志中发现 $warning_count 个警告 (较多)"
    fi
}

# 显示系统信息
show_system_info() {
    log_header "系统信息"

    echo -e "${CYAN}系统时间:${NC} $(date)"
    echo -e "${CYAN}运行用户:${NC} $(whoami)"
    echo -e "${CYAN}当前目录:${NC} $(pwd)"
    echo -e "${CYAN}操作系统:${NC} $(uname -s -r)"

    if [ -f .env ]; then
        echo -e "${CYAN}环境配置:${NC} .env 文件存在"
    else
        echo -e "${YELLOW}环境配置:${NC} .env 文件不存在"
    fi

    if [ -f docker-compose.dev.yml ]; then
        echo -e "${CYAN}Docker配置:${NC} docker-compose.dev.yml 文件存在"
    else
        echo -e "${YELLOW}Docker配置:${NC} docker-compose.dev.yml 文件不存在"
    fi
}

# 生成健康报告
generate_health_report() {
    log_header "健康检查报告"

    local health_score=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))

    echo -e "${CYAN}总检查项:${NC} $TOTAL_CHECKS"
    echo -e "${GREEN}通过检查:${NC} $PASSED_CHECKS"
    echo -e "${YELLOW}警告检查:${NC} $WARNING_CHECKS"
    echo -e "${RED}失败检查:${NC} $FAILED_CHECKS"
    echo -e "${PURPLE}健康评分:${NC} $health_score%"

    echo
    if [ "$health_score" -ge 90 ]; then
        echo -e "${GREEN}🎉 系统状态: 优秀 - 所有核心功能运行正常${NC}"
    elif [ "$health_score" -ge 70 ]; then
        echo -e "${YELLOW}⚠️  系统状态: 良好 - 存在一些小问题，建议关注${NC}"
    elif [ "$health_score" -ge 50 ]; then
        echo -e "${YELLOW}⚠️  系统状态: 一般 - 存在多个问题，需要处理${NC}"
    else
        echo -e "${RED}❌ 系统状态: 异常 - 存在严重问题，需要立即处理${NC}"
    fi

    echo
    echo -e "${BLUE}建议操作:${NC}"
    if [ "$FAILED_CHECKS" -gt 0 ]; then
        echo "  • 查看详细错误日志: $DOCKER_COMPOSE -f docker-compose.dev.yml logs"
        echo "  • 重启相关服务: $DOCKER_COMPOSE -f docker-compose.dev.yml restart"
        echo "  • 查看故障排除指南: docs/troubleshooting.md"
    fi

    if [ "$WARNING_CHECKS" -gt 0 ]; then
        echo "  • 关注警告信息，定期维护系统"
        echo "  • 监控资源使用情况"
    fi

    echo "  • 访问系统: http://localhost"
    echo "  • 查看API文档: http://localhost:8000/docs"
}

# 主函数
main() {
    clear
    echo -e "${BLUE}"
    cat << 'EOF'
 _                       _   _       _
| |                     | | | |     | |
| |     ___   ___  _ __ | |_| | ___ | | ___
| |    / _ \ / _ \| '_ \|  _  |/ _ \| |/ _ \
| |___| (_) | (_) | |_) | | | | (_) | |  __/
\_____/\___/ \___/| .__/\_| |_/\___/|_|\___|
                  | |
                  |_|

智能网页数据提取系统 - 健康检查
EOF
    echo -e "${NC}"

    show_system_info

    # 执行所有检查
    check_docker
    check_containers
    check_network
    check_database
    check_redis
    check_api
    check_resources
    check_logs

    # 生成报告
    generate_health_report

    echo
    echo -e "${BLUE}健康检查完成!${NC}"

    # 根据健康状态设置退出码
    if [ "$FAILED_CHECKS" -gt 0 ]; then
        exit 1
    elif [ "$WARNING_CHECKS" -gt 3 ]; then
        exit 2
    else
        exit 0
    fi
}

# 检查脚本参数
case "${1:-}" in
    -h|--help)
        echo "Loop Hole 系统健康检查脚本"
        echo
        echo "用法: $0 [选项]"
        echo
        echo "选项:"
        echo "  -h, --help     显示帮助信息"
        echo "  --quick        快速检查 (仅检查核心服务)"
        echo "  --detailed     详细检查 (包含所有项目)"
        echo "  --json         输出JSON格式结果"
        echo
        echo "退出码:"
        echo "  0  - 健康状态良好"
        echo "  1  - 存在错误需要处理"
        echo "  2  - 存在较多警告"
        echo
        exit 0
        ;;
    --quick)
        echo "执行快速健康检查..."
        # 可以在这里实现快速检查逻辑
        ;;
    --detailed)
        echo "执行详细健康检查..."
        # 默认就是详细检查
        ;;
    --json)
        echo "JSON格式输出暂未实现"
        exit 1
        ;;
esac

# 检查是否在正确的目录中
if [ ! -f "requirements.txt" ] || [ ! -d "app" ]; then
    echo -e "${RED}错误: 请在 Loop Hole 项目根目录中运行此脚本${NC}"
    echo -e "${BLUE}当前目录: $(pwd)${NC}"
    echo -e "${BLUE}请确保目录包含 requirements.txt 和 app/ 文件夹${NC}"
    exit 1
fi

# 运行主函数
main "$@"
