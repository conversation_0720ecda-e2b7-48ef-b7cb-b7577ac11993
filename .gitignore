# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
.npm
.eslintcache
.stylelintcache

# Build outputs
dist/
build/
.nuxt
.next
.out
.vercel

# Database
*.db
*.sqlite
*.sqlite3
data/

# Logs
logs/
*.log

# Cache
.cache/
.parcel-cache/
.pytest_cache/
.coverage
.nyc_output

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Docker
.dockerignore

# UV
.uvignore
uv.lock

# Alembic
alembic/versions/*.py
!alembic/versions/001_initial_schema.py
!alembic/versions/002_add_task_fields.py