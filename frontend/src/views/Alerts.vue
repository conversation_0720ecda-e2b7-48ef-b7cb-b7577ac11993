<template>
  <div class="alerts-container">
    <div class="page-header">
      <h1>告警管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建告警规则
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 告警概览 -->
    <div class="alert-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ alertMetrics.total_rules }}</div>
              <div class="metric-label">告警规则总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ alertMetrics.enabled_rules }}</div>
              <div class="metric-label">启用规则数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card active-alerts">
            <div class="metric-content">
              <div class="metric-value">{{ alertMetrics.active_alerts }}</div>
              <div class="metric-label">活跃告警</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ alertMetrics.recent_24h_alerts }}</div>
              <div class="metric-label">24小时告警</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 活跃告警 -->
    <el-card class="section-card" v-if="activeAlerts.length > 0">
      <template #header>
        <div class="card-header">
          <span>活跃告警</span>
          <el-badge :value="activeAlerts.length" class="item" type="danger" />
        </div>
      </template>
      <el-table :data="activeAlerts" style="width: 100%">
        <el-table-column prop="rule_name" label="规则名称" width="200" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="level" label="级别" width="100">
          <template #default="scope">
            <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="first_triggered" label="首次触发" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.first_triggered) }}
          </template>
        </el-table-column>
        <el-table-column prop="triggered" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.triggered ? 'danger' : 'warning'">
              {{ scope.row.triggered ? '已触发' : '监控中' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 告警规则 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>告警规则</span>
        </div>
      </template>
      <el-table :data="alertRules" style="width: 100%">
        <el-table-column prop="name" label="规则名称" width="180" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="metric" label="监控指标" width="120" />
        <el-table-column label="阈值条件" width="150">
          <template #default="scope">
            {{ scope.row.operator }} {{ scope.row.threshold }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="告警级别" width="100">
          <template #default="scope">
            <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="持续时间(秒)" width="120" />
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="toggleRule(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="editRule(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteRule(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 告警历史 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>告警历史</span>
          <el-button size="small" type="danger" @click="clearHistory">清除历史</el-button>
        </div>
      </template>
      <el-table :data="alertHistory" style="width: 100%" max-height="400">
        <el-table-column prop="rule_name" label="规则名称" width="180" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="level" label="级别" width="100">
          <template #default="scope">
            <el-tag :type="getAlertLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="触发条件" width="200">
          <template #default="scope">
            {{ scope.row.current_value }} {{ scope.row.operator }} {{ scope.row.threshold }}
          </template>
        </el-table-column>
        <el-table-column prop="timestamp" label="触发时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑告警规则对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingRule ? '编辑告警规则' : '创建告警规则'"
      width="600px"
    >
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="ruleForm.name" :disabled="editingRule" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="ruleForm.description" type="textarea" />
        </el-form-item>
        <el-form-item label="监控指标" prop="metric">
          <el-select v-model="ruleForm.metric" placeholder="选择监控指标">
            <el-option label="CPU使用率" value="cpu_percent" />
            <el-option label="内存使用率" value="memory_percent" />
            <el-option label="磁盘使用率" value="disk_percent" />
            <el-option label="平均响应时间" value="avg_response_time" />
            <el-option label="错误率" value="error_rate" />
            <el-option label="待处理任务数" value="pending_tasks" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作符" prop="operator">
          <el-select v-model="ruleForm.operator" placeholder="选择操作符">
            <el-option label="大于 >" value=">" />
            <el-option label="大于等于 >=" value=">=" />
            <el-option label="小于 <" value="<" />
            <el-option label="小于等于 <=" value="<=" />
            <el-option label="等于 ==" value="==" />
            <el-option label="不等于 !=" value="!=" />
          </el-select>
        </el-form-item>
        <el-form-item label="阈值" prop="threshold">
          <el-input-number v-model="ruleForm.threshold" :precision="2" />
        </el-form-item>
        <el-form-item label="告警级别" prop="level">
          <el-select v-model="ruleForm.level" placeholder="选择告警级别">
            <el-option label="信息" value="info" />
            <el-option label="警告" value="warning" />
            <el-option label="错误" value="error" />
            <el-option label="严重" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="持续时间" prop="duration">
          <el-input-number v-model="ruleForm.duration" :min="30" :step="30" />
          <span style="margin-left: 10px; color: #999;">秒</span>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="ruleForm.enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRule">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import api from '@/utils/api'
import { formatDateTime } from '@/utils/format'

// 响应式数据
const alertRules = ref([])
const activeAlerts = ref([])
const alertHistory = ref([])
const alertMetrics = ref({
  total_rules: 0,
  enabled_rules: 0,
  active_alerts: 0,
  recent_24h_alerts: 0
})

const showCreateDialog = ref(false)
const editingRule = ref(null)
const ruleFormRef = ref()

const ruleForm = reactive({
  name: '',
  description: '',
  metric: '',
  operator: '>',
  threshold: 0,
  level: 'warning',
  duration: 60,
  enabled: true
})

const ruleRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  metric: [{ required: true, message: '请选择监控指标', trigger: 'change' }],
  operator: [{ required: true, message: '请选择操作符', trigger: 'change' }],
  threshold: [{ required: true, message: '请输入阈值', trigger: 'blur' }],
  level: [{ required: true, message: '请选择告警级别', trigger: 'change' }],
  duration: [{ required: true, message: '请输入持续时间', trigger: 'blur' }]
}

// 刷新间隔
let refreshInterval = null

// 方法
const getAlertLevelType = (level) => {
  const types = {
    info: '',
    warning: 'warning',
    error: 'danger',
    critical: 'danger'
  }
  return types[level] || ''
}

const refreshData = async () => {
  try {
    await Promise.all([
      loadAlertRules(),
      loadActiveAlerts(),
      loadAlertHistory(),
      loadAlertMetrics()
    ])
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

const loadAlertRules = async () => {
  try {
    const response = await api.get('/alerts/rules')
    alertRules.value = response.data
  } catch (error) {
    console.error('加载告警规则失败:', error)
    ElMessage.error('加载告警规则失败')
  }
}

const loadActiveAlerts = async () => {
  try {
    const response = await api.get('/alerts/active')
    activeAlerts.value = response.data.alerts
  } catch (error) {
    console.error('加载活跃告警失败:', error)
  }
}

const loadAlertHistory = async () => {
  try {
    const response = await api.get('/alerts/history?limit=50')
    alertHistory.value = response.data.alerts
  } catch (error) {
    console.error('加载告警历史失败:', error)
  }
}

const loadAlertMetrics = async () => {
  try {
    const response = await api.get('/alerts/metrics')
    alertMetrics.value = response.data
  } catch (error) {
    console.error('加载告警指标失败:', error)
  }
}

const toggleRule = async (rule) => {
  try {
    const action = rule.enabled ? 'enable' : 'disable'
    await api.post(`/alerts/rules/${rule.name}/${action}`)
    ElMessage.success(`告警规则${rule.enabled ? '启用' : '禁用'}成功`)
    await loadAlertMetrics()
  } catch (error) {
    console.error('切换规则状态失败:', error)
    ElMessage.error('操作失败')
    // 恢复原状态
    rule.enabled = !rule.enabled
  }
}

const editRule = (rule) => {
  editingRule.value = rule
  Object.assign(ruleForm, rule)
  showCreateDialog.value = true
}

const deleteRule = async (rule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除告警规则 "${rule.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/alerts/rules/${rule.name}`)
    ElMessage.success('删除成功')
    await refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除规则失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveRule = async () => {
  try {
    await ruleFormRef.value.validate()
    
    if (editingRule.value) {
      // 更新规则
      await api.put(`/alerts/rules/${editingRule.value.name}`, ruleForm)
      ElMessage.success('更新成功')
    } else {
      // 创建规则
      await api.post('/alerts/rules', ruleForm)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
    await refreshData()
  } catch (error) {
    console.error('保存规则失败:', error)
    ElMessage.error('保存失败')
  }
}

const resetForm = () => {
  editingRule.value = null
  Object.assign(ruleForm, {
    name: '',
    description: '',
    metric: '',
    operator: '>',
    threshold: 0,
    level: 'warning',
    duration: 60,
    enabled: true
  })
  ruleFormRef.value?.resetFields()
}

const clearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有告警历史记录吗？',
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete('/alerts/history')
    ElMessage.success('清除成功')
    await loadAlertHistory()
    await loadAlertMetrics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除历史失败:', error)
      ElMessage.error('清除失败')
    }
  }
}

// 生命周期
onMounted(() => {
  refreshData()
  // 每30秒刷新一次数据
  refreshInterval = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.alerts-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.alert-overview {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
}

.metric-card.active-alerts {
  border-color: #f56c6c;
}

.metric-content {
  padding: 10px;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.active-alerts .metric-value {
  color: #f56c6c;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.section-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.el-table {
  margin-top: 10px;
}
</style>