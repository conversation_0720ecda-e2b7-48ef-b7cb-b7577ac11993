<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>系统概览</h1>
      <div class="header-actions">
        <div class="connection-status">
          <el-tag :type="isConnected ? 'success' : 'danger'" size="small">
            <el-icon><Connection /></el-icon>
            {{ isConnected ? '实时连接' : '连接断开' }}
          </el-tag>
        </div>
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onDateRangeChange"
          size="small"
        />
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalTasks }}</div>
              <div class="stat-label">总任务数</div>
              <div class="stat-change" :class="getChangeClass(stats.taskChange)">
                {{ formatChange(stats.taskChange) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.completedTasks }}</div>
              <div class="stat-label">已完成任务</div>
              <div class="stat-change" :class="getChangeClass(stats.completedChange)">
                {{ formatChange(stats.completedChange) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.runningJobs }}</div>
              <div class="stat-label">运行中作业</div>
              <div class="stat-change" :class="getChangeClass(stats.runningChange)">
                {{ formatChange(stats.runningChange) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalExtractions }}</div>
              <div class="stat-label">总提取次数</div>
              <div class="stat-change" :class="getChangeClass(stats.extractionChange)">
                {{ formatChange(stats.extractionChange) }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>任务执行趋势</span>
              <el-radio-group v-model="chartPeriod" size="small">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="taskTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>提取成功率</span>
            </div>
          </template>
          <div ref="successRateChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" class="status-row">
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-tag :type="systemStatus.type" size="small">
                {{ systemStatus.text }}
              </el-tag>
            </div>
          </template>
          <div class="system-metrics">
            <div class="metric-item">
              <span class="metric-label">CPU使用率</span>
              <el-progress :percentage="systemMetrics.cpu" :color="getProgressColor(systemMetrics.cpu)" />
            </div>
            <div class="metric-item">
              <span class="metric-label">内存使用率</span>
              <el-progress :percentage="systemMetrics.memory" :color="getProgressColor(systemMetrics.memory)" />
            </div>
            <div class="metric-item">
              <span class="metric-label">磁盘使用率</span>
              <el-progress :percentage="systemMetrics.disk" :color="getProgressColor(systemMetrics.disk)" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近任务</span>
              <el-button class="button" text @click="$router.push('/tasks')">查看全部</el-button>
            </div>
          </template>
          <div class="recent-tasks">
            <div v-for="task in recentTasks" :key="task.id" class="task-item">
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-time">{{ formatTime(task.created_at) }}</div>
              </div>
              <el-tag :type="getTaskStatusType(task.status)" size="small">
                {{ getTaskStatusText(task.status) }}
              </el-tag>
            </div>
            <div v-if="recentTasks.length === 0" class="empty-state">
              <el-empty description="暂无任务" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近提取结果</span>
              <el-button class="button" text @click="$router.push('/results')">查看全部</el-button>
            </div>
          </template>
          <div class="recent-results">
            <div v-for="result in recentResults" :key="result.id" class="result-item">
              <div class="result-info">
                <div class="result-task">{{ result.task_name }}</div>
                <div class="result-time">{{ formatTime(result.extracted_at) }}</div>
                <div class="result-data">{{ getDataSummary(result.data) }}</div>
              </div>
              <el-tag :type="getResultStatusType(result.status)" size="small">
                {{ getResultStatusText(result.status) }}
              </el-tag>
            </div>
            <div v-if="recentResults.length === 0" class="empty-state">
              <el-empty description="暂无提取结果" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="actions-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/tasks/create')">
              <el-icon><Plus /></el-icon>
              创建新任务
            </el-button>
            <el-button @click="$router.push('/extract')">
              <el-icon><Search /></el-icon>
              页面分析
            </el-button>
            <el-button @click="$router.push('/results')">
              <el-icon><View /></el-icon>
              查看结果
            </el-button>
            <el-button @click="exportSystemReport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, 
  CircleCheck, 
  Loading, 
  DataAnalysis,
  Refresh,
  Plus,
  Search,
  View,
  Download,
  Connection
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import api from '@/utils/api'
import { wsClient } from '@/utils/websocket'
import { useUserStore } from '@/stores/user'

const loading = ref(false)
const dateRange = ref([])
const chartPeriod = ref('7d')
const taskTrendChart = ref(null)
const successRateChart = ref(null)
const isConnected = ref(false)

const stats = reactive({
  totalTasks: 0,
  completedTasks: 0,
  runningJobs: 0,
  totalExtractions: 0,
  taskChange: 0,
  completedChange: 0,
  runningChange: 0,
  extractionChange: 0
})

const systemStatus = reactive({
  type: 'success',
  text: '正常运行'
})

const systemMetrics = reactive({
  cpu: 45,
  memory: 62,
  disk: 38
})

const recentTasks = ref([])
const recentResults = ref([])

let taskTrendChartInstance = null
let successRateChartInstance = null

const loadDashboardData = async () => {
  try {
    loading.value = true
    
    // 加载统计数据
    const params = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const [statsResponse, tasksResponse, resultsResponse, metricsResponse] = await Promise.all([
      api.get('/dashboard/stats', { params }),
      api.get('/tasks/', { params: { page: 1, limit: 5 } }),
      api.get('/results/', { params: { page: 1, limit: 5 } }),
      api.get('/dashboard/metrics')
    ])
    
    Object.assign(stats, statsResponse.data)
    recentTasks.value = tasksResponse.data.items || []
    recentResults.value = resultsResponse.data.items || []
    
    if (metricsResponse.data) {
      Object.assign(systemMetrics, metricsResponse.data.system)
      Object.assign(systemStatus, metricsResponse.data.status)
    }
    
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadChartData = async () => {
  try {
    const params = { period: chartPeriod.value }
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const [trendResponse, successResponse] = await Promise.all([
      api.get('/dashboard/task-trend', { params }),
      api.get('/dashboard/success-rate', { params })
    ])
    
    updateTaskTrendChart(trendResponse.data)
    updateSuccessRateChart(successResponse.data)
    
  } catch (error) {
    console.error('加载图表数据失败:', error)
  }
}

const initCharts = () => {
  nextTick(() => {
    if (taskTrendChart.value) {
      taskTrendChartInstance = echarts.init(taskTrendChart.value)
    }
    if (successRateChart.value) {
      successRateChartInstance = echarts.init(successRateChart.value)
    }
    
    loadChartData()
  })
}

const updateTaskTrendChart = (data) => {
  if (!taskTrendChartInstance) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['创建任务', '完成任务', '失败任务']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.dates || []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '创建任务',
        type: 'line',
        stack: 'Total',
        data: data.created || []
      },
      {
        name: '完成任务',
        type: 'line',
        stack: 'Total',
        data: data.completed || []
      },
      {
        name: '失败任务',
        type: 'line',
        stack: 'Total',
        data: data.failed || []
      }
    ]
  }
  
  taskTrendChartInstance.setOption(option)
}

const updateSuccessRateChart = (data) => {
  if (!successRateChartInstance) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '提取结果',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: data.success || 0, name: '成功', itemStyle: { color: '#67C23A' } },
          { value: data.failed || 0, name: '失败', itemStyle: { color: '#F56C6C' } },
          { value: data.partial || 0, name: '部分成功', itemStyle: { color: '#E6A23C' } }
        ]
      }
    ]
  }
  
  successRateChartInstance.setOption(option)
}

const refreshData = () => {
  loadDashboardData()
  loadChartData()
}

const onDateRangeChange = () => {
  loadDashboardData()
  loadChartData()
}

const exportSystemReport = async () => {
  try {
    const { value: format } = await ElMessageBox.prompt(
      '请选择导出格式',
      '导出系统报告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: {
          pdf: 'PDF报告',
          excel: 'Excel表格',
          json: 'JSON数据'
        },
        inputValue: 'pdf'
      }
    )
    
    const params = { format }
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const response = await api.post('/dashboard/export', params, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system_report_${new Date().toISOString().split('T')[0]}.${format}`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报告导出成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出报告失败:', error)
      ElMessage.error('导出报告失败')
    }
  }
}

// 辅助函数
const formatTime = (time) => {
  return new Date(time).toLocaleString('zh-CN')
}

const formatChange = (change) => {
  if (change === 0) return '无变化'
  const prefix = change > 0 ? '+' : ''
  return `${prefix}${change}%`
}

const getChangeClass = (change) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

const getTaskStatusType = (status) => {
  const types = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

const getTaskStatusText = (status) => {
  const texts = {
    pending: '等待中',
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getResultStatusType = (status) => {
  const types = {
    success: 'success',
    failed: 'danger',
    partial: 'warning'
  }
  return types[status] || 'info'
}

const getResultStatusText = (status) => {
  const texts = {
    success: '成功',
    failed: '失败',
    partial: '部分成功'
  }
  return texts[status] || status
}

const getDataSummary = (data) => {
  if (!data) return '无数据'
  if (Array.isArray(data)) {
    return `${data.length} 条记录`
  }
  return `${Object.keys(data).length} 个字段`
}

const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

// 监听图表周期变化
watch(chartPeriod, () => {
  loadChartData()
})

// WebSocket事件监听器
const handleTaskStatusUpdate = (message) => {
  // 更新最近任务状态
  const task = recentTasks.value.find(t => t.id === message.task_id)
  if (task) {
    task.status = message.status
  }
  // 刷新统计数据
  loadDashboardData()
}

const handleSystemAlert = (message) => {
  ElMessage({
    type: message.level === 'error' ? 'error' : 'warning',
    title: '系统告警',
    message: message.message,
    duration: 5000
  })
}

const handleExtractionCompleted = (message) => {
  // 更新最近结果
  loadDashboardData()
  // 刷新图表
  loadChartData()
}

let refreshInterval = null

onMounted(() => {
  loadDashboardData()
  initCharts()
  
  // 连接WebSocket
  const userStore = useUserStore()
  const userId = userStore.user?.id ? String(userStore.user.id) : 'dashboard_' + Math.random().toString(36).substr(2, 9)
  const token = userStore.token
  wsClient.connect(userId, token)
  
  // 注册事件监听器
  wsClient.on('task_status_update', handleTaskStatusUpdate)
  wsClient.on('system_alert', handleSystemAlert)
  wsClient.on('extraction_completed', handleExtractionCompleted)
  wsClient.on('connection_established', () => {
    isConnected.value = true
  })
  wsClient.on('error', (data) => {
    console.error('WebSocket error:', data)
    isConnected.value = false
  })
  
  // 监听连接状态变化
  isConnected.value = wsClient.isConnected.value
  
  // 定时刷新系统指标
  refreshInterval = setInterval(() => {
    if (document.visibilityState === 'visible') {
      loadDashboardData()
    }
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  // 清理定时器
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  
  // 清理图表实例
  if (taskTrendChartInstance) {
    taskTrendChartInstance.dispose()
  }
  if (successRateChartInstance) {
    successRateChartInstance.dispose()
  }
  
  // 清理WebSocket连接
  wsClient.off('task_status_update', handleTaskStatusUpdate)
  wsClient.off('system_alert', handleSystemAlert)
  wsClient.off('extraction_completed', handleExtractionCompleted)
  wsClient.off('connection_established')
  wsClient.off('error')
  wsClient.disconnect()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  color: #303133;
  margin: 0;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
  font-size: 24px;
}

.stat-icon.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-icon.primary {
  background: linear-gradient(135deg, #409eff, #66b3ff);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.stat-change.neutral {
  color: #909399;
}

.charts-row {
  margin-bottom: 30px;
}

.chart-container {
  height: 350px;
  padding: 20px;
}

.status-row {
  margin-bottom: 30px;
}

.actions-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.system-metrics {
  padding: 20px;
}

.metric-item {
  margin-bottom: 20px;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-label {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.recent-tasks,
.recent-results {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.task-item,
.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.task-item:hover,
.result-item:hover {
  background: #e9ecef;
}

.task-item:last-child,
.result-item:last-child {
  margin-bottom: 0;
}

.task-info,
.result-info {
  flex: 1;
}

.task-name,
.result-task {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.task-time,
.result-time {
  color: #909399;
  font-size: 12px;
  margin-bottom: 2px;
}

.result-data {
  color: #606266;
  font-size: 11px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.quick-actions {
  display: flex;
  gap: 15px;
  padding: 20px;
  flex-wrap: wrap;
}

.quick-actions .el-button {
  flex: 1;
  min-width: 140px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-actions .el-button {
    width: 100%;
  }
}

/* 卡片样式增强 */
.el-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 进度条样式 */
.el-progress {
  margin-bottom: 8px;
}

/* 标签样式 */
.el-tag {
  border: none;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 日期选择器样式 */
.el-date-editor {
  width: 280px;
}

/* 单选按钮组样式 */
.el-radio-group .el-radio-button__inner {
  border-radius: 4px;
  margin: 0 2px;
}
</style>
