<template>
  <div class="extract">
    <div class="page-header">
      <h1>数据提取</h1>
      <p>快速分析和提取网页数据</p>
    </div>
    
    <!-- 页面分析 -->
    <el-card class="analysis-card">
      <template #header>
        <span>页面分析</span>
      </template>
      
      <el-form :model="analysisForm" label-width="100px">
        <el-form-item label="目标URL">
          <el-input
            v-model="analysisForm.url"
            placeholder="请输入要分析的网页URL"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            @click="analyzePage"
            :loading="analyzing"
          >
            {{ analyzing ? '分析中...' : '分析页面' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 分析结果 -->
      <div v-if="analysisResult" class="analysis-result">
        <h3>分析结果</h3>
        
        <el-tabs v-model="activeTab">
          <el-tab-pane label="表格" name="tables">
            <div v-if="analysisResult.detected_elements?.tables?.length">
              <el-card
                v-for="(table, index) in analysisResult.detected_elements.tables"
                :key="index"
                class="element-card"
              >
                <template #header>
                  <span>表格 {{ index + 1 }} (置信度: {{ (table.confidence * 100).toFixed(1) }}%)</span>
                </template>
                
                <p><strong>选择器:</strong> {{ table.selector }}</p>
                <p><strong>表头:</strong> {{ table.headers.join(', ') }}</p>
                <p><strong>行数:</strong> {{ table.row_count }}</p>
                
                <el-button
                  size="small"
                  type="primary"
                  @click="useTableRule(table)"
                >
                  使用此规则
                </el-button>
              </el-card>
            </div>
            <el-empty v-else description="未检测到表格" />
          </el-tab-pane>
          
          <el-tab-pane label="卡片" name="cards">
            <div v-if="analysisResult.detected_elements?.cards?.length">
              <el-card
                v-for="(card, index) in analysisResult.detected_elements.cards"
                :key="index"
                class="element-card"
              >
                <template #header>
                  <span>卡片 {{ index + 1 }} (置信度: {{ (card.confidence * 100).toFixed(1) }}%)</span>
                </template>
                
                <p><strong>选择器:</strong> {{ card.selector }}</p>
                <p><strong>字段:</strong> {{ card.fields.join(', ') }}</p>
                <p><strong>数量:</strong> {{ card.count }}</p>
                
                <el-button
                  size="small"
                  type="primary"
                  @click="useCardRule(card)"
                >
                  使用此规则
                </el-button>
              </el-card>
            </div>
            <el-empty v-else description="未检测到卡片" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    
    <!-- 数据提取 -->
    <el-card class="extraction-card">
      <template #header>
        <span>数据提取</span>
      </template>
      
      <el-form :model="extractionForm" label-width="100px">
        <el-form-item label="目标URL">
          <el-input
            v-model="extractionForm.url"
            placeholder="请输入要提取数据的网页URL"
          />
        </el-form-item>
        
        <el-form-item label="提取规则">
          <el-input
            v-model="extractionForm.rules"
            type="textarea"
            :rows="8"
            placeholder="JSON格式的提取规则"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="success"
            @click="extractData"
            :loading="extracting"
          >
            {{ extracting ? '提取中...' : '开始提取' }}
          </el-button>
          
          <el-button @click="clearResults">
            清空结果
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 提取结果 -->
      <div v-if="extractionResult" class="extraction-result">
        <h3>提取结果</h3>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="状态">
            <el-tag :type="extractionResult.status === 'completed' ? 'success' : 'danger'">
              {{ extractionResult.status === 'completed' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="记录数">
            {{ extractionResult.record_count }}
          </el-descriptions-item>
          <el-descriptions-item label="提取时间">
            {{ formatDateTime(extractionResult.metadata?.extraction_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ extractionResult.metadata?.duration }}秒
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="result-data">
          <h4>提取的数据:</h4>
          <el-input
            v-model="formattedResult"
            type="textarea"
            :rows="15"
            readonly
          />
        </div>
        
        <div class="result-actions">
          <el-button type="primary" @click="downloadResult('json')">
            <el-icon><Download /></el-icon>
            下载JSON
          </el-button>
          <el-button @click="downloadResult('csv')">
            <el-icon><Download /></el-icon>
            下载CSV
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/utils/api'

const analyzing = ref(false)
const extracting = ref(false)
const activeTab = ref('tables')

const analysisForm = reactive({
  url: ''
})

const extractionForm = reactive({
  url: '',
  rules: ''
})

const analysisResult = ref(null)
const extractionResult = ref(null)

const formattedResult = computed(() => {
  if (!extractionResult.value?.extracted_data) return ''
  return JSON.stringify(extractionResult.value.extracted_data, null, 2)
})

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const analyzePage = async () => {
  if (!analysisForm.url) {
    ElMessage.warning('请输入URL')
    return
  }
  
  analyzing.value = true
  try {
    const response = await api.post('/extract/analyze', {
      url: analysisForm.url
    })
    
    analysisResult.value = response.data
    ElMessage.success('页面分析完成')
  } catch (error) {
    ElMessage.error('页面分析失败')
  } finally {
    analyzing.value = false
  }
}

const useTableRule = (table) => {
  const rule = {
    type: 'table',
    selector: table.selector,
    fields: table.headers.reduce((acc, header, index) => {
      acc[header] = `td:nth-child(${index + 1})`
      return acc
    }, {})
  }
  
  extractionForm.url = analysisForm.url
  extractionForm.rules = JSON.stringify({ table_data: rule }, null, 2)
  
  ElMessage.success('已应用表格提取规则')
}

const useCardRule = (card) => {
  const rule = {
    type: 'css',
    selector: card.selector,
    fields: card.fields.reduce((acc, field) => {
      acc[field] = `.${field}`
      return acc
    }, {})
  }
  
  extractionForm.url = analysisForm.url
  extractionForm.rules = JSON.stringify({ card_data: rule }, null, 2)
  
  ElMessage.success('已应用卡片提取规则')
}

const extractData = async () => {
  if (!extractionForm.url || !extractionForm.rules) {
    ElMessage.warning('请输入URL和提取规则')
    return
  }
  
  try {
    const rules = JSON.parse(extractionForm.rules)
    
    extracting.value = true
    const response = await api.post('/extract/execute', {
      url: extractionForm.url,
      extraction_rules: rules
    })
    
    extractionResult.value = response.data
    ElMessage.success('数据提取完成')
  } catch (error) {
    if (error.message?.includes('JSON')) {
      ElMessage.error('提取规则格式错误，请检查JSON格式')
    } else {
      ElMessage.error('数据提取失败')
    }
  } finally {
    extracting.value = false
  }
}

const clearResults = () => {
  analysisResult.value = null
  extractionResult.value = null
  ElMessage.info('已清空结果')
}

const downloadResult = (format) => {
  if (!extractionResult.value) return
  
  const data = extractionResult.value.extracted_data
  const filename = `extraction_result_${Date.now()}`
  
  if (format === 'json') {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })
    downloadBlob(blob, `${filename}.json`)
  } else if (format === 'csv') {
    // 简单的CSV转换，实际项目中应该使用专门的库
    ElMessage.info('CSV下载功能开发中')
  }
}

const downloadBlob = (blob, filename) => {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.analysis-card,
.extraction-card {
  margin-bottom: 20px;
}

.analysis-result,
.extraction-result {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.element-card {
  margin-bottom: 15px;
}

.result-data {
  margin: 20px 0;
}

.result-actions {
  margin-top: 15px;
}

.result-actions .el-button {
  margin-right: 10px;
}
</style>
