<template>
  <div class="jobs">
    <div class="page-header">
      <h1>作业监控</h1>
      <p>实时监控任务执行状态</p>
    </div>
    
    <!-- 队列状态概览 -->
    <el-row :gutter="20" class="queue-stats">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="30" color="#409EFF"><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ queueStatus.active_count || 0 }}</h3>
              <p>活跃作业</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="30" color="#E6A23C"><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ queueStatus.scheduled_count || 0 }}</h3>
              <p>预定作业</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="30" color="#67C23A"><SuccessFilled /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ completedJobs }}</h3>
              <p>已完成</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="30" color="#F56C6C"><CircleCloseFilled /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ failedJobs }}</h3>
              <p>失败</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 作业列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>作业列表</span>
          <el-button @click="refreshJobs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="jobs"
        style="width: 100%"
      >
        <el-table-column prop="job_id" label="作业ID" width="200" show-overflow-tooltip />
        <el-table-column prop="task_name" label="任务名称" width="180" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="started_at" label="开始时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.started_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="耗时" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress || 0"
              :status="row.status === 'FAILURE' ? 'exception' : undefined"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="viewJobDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button
              v-if="row.status === 'PENDING' || row.status === 'RETRY'"
              size="small"
              type="danger"
              @click="cancelJob(row)"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 作业详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      title="作业详情"
      width="60%"
    >
      <div v-if="selectedJob">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="作业ID">
            {{ selectedJob.job_id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedJob.status)">
              {{ getStatusText(selectedJob.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务名称">
            {{ selectedJob.task_name }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ formatDateTime(selectedJob.started_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="耗时">
            {{ formatDuration(selectedJob.duration) }}
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ selectedJob.progress || 0 }}%
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedJob.result" class="job-result">
          <h4>执行结果:</h4>
          <el-input
            v-model="formattedJobResult"
            type="textarea"
            :rows="10"
            readonly
          />
        </div>
        
        <div v-if="selectedJob.error" class="job-error">
          <h4>错误信息:</h4>
          <el-alert
            :title="selectedJob.error"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
        
        <div v-if="selectedJob.traceback" class="job-traceback">
          <h4>错误堆栈:</h4>
          <el-input
            v-model="selectedJob.traceback"
            type="textarea"
            :rows="8"
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

const loading = ref(false)
const jobs = ref([])
const queueStatus = ref({})
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

const showDetailsDialog = ref(false)
const selectedJob = ref(null)

let refreshTimer = null

const completedJobs = computed(() => {
  return jobs.value.filter(job => job.status === 'SUCCESS').length
})

const failedJobs = computed(() => {
  return jobs.value.filter(job => job.status === 'FAILURE').length
})

const formattedJobResult = computed(() => {
  if (!selectedJob.value?.result) return ''
  return JSON.stringify(selectedJob.value.result, null, 2)
})

const getStatusType = (status) => {
  const typeMap = {
    'PENDING': 'info',
    'STARTED': 'warning',
    'SUCCESS': 'success',
    'FAILURE': 'danger',
    'RETRY': 'warning',
    'REVOKED': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'PENDING': '等待中',
    'STARTED': '执行中',
    'SUCCESS': '成功',
    'FAILURE': '失败',
    'RETRY': '重试中',
    'REVOKED': '已取消'
  }
  return textMap[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDuration = (duration) => {
  if (!duration) return '-'
  if (duration < 60) return `${duration.toFixed(1)}秒`
  if (duration < 3600) return `${(duration / 60).toFixed(1)}分钟`
  return `${(duration / 3600).toFixed(1)}小时`
}

const loadJobs = async () => {
  loading.value = true
  try {
    // 模拟作业数据，实际应该从API获取
    jobs.value = [
      {
        job_id: 'job_12345',
        task_name: '电商数据提取',
        status: 'SUCCESS',
        started_at: '2024-01-15T14:30:00Z',
        duration: 45.2,
        progress: 100,
        result: { records: 150, status: 'completed' }
      },
      {
        job_id: 'job_12346',
        task_name: '用户行为分析',
        status: 'STARTED',
        started_at: '2024-01-15T14:35:00Z',
        duration: 12.5,
        progress: 65
      },
      {
        job_id: 'job_12347',
        task_name: '库存监控',
        status: 'FAILURE',
        started_at: '2024-01-15T14:25:00Z',
        duration: 8.3,
        progress: 30,
        error: 'Connection timeout',
        traceback: 'Traceback (most recent call last):\n  File "extractor.py", line 45...'
      }
    ]
    
    total.value = jobs.value.length
  } catch (error) {
    ElMessage.error('加载作业列表失败')
  } finally {
    loading.value = false
  }
}

const loadQueueStatus = async () => {
  try {
    const response = await api.get('/jobs/queue/status')
    queueStatus.value = response.data
  } catch (error) {
    console.error('Failed to load queue status:', error)
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadJobs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadJobs()
}

const refreshJobs = () => {
  loadJobs()
  loadQueueStatus()
}

const viewJobDetails = async (job) => {
  try {
    const response = await api.get(`/jobs/${job.job_id}`)
    selectedJob.value = response.data
    showDetailsDialog.value = true
  } catch (error) {
    // 如果API调用失败，使用本地数据
    selectedJob.value = job
    showDetailsDialog.value = true
  }
}

const cancelJob = async (job) => {
  try {
    await ElMessageBox.confirm('确定要取消这个作业吗？', '确认取消', {
      type: 'warning'
    })
    
    await api.delete(`/jobs/${job.job_id}`)
    ElMessage.success('作业已取消')
    refreshJobs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消作业失败')
    }
  }
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    refreshJobs()
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  loadJobs()
  loadQueueStatus()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.queue-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 15px;
}

.stat-info h3 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.job-result,
.job-error,
.job-traceback {
  margin-top: 20px;
}

.job-result h4,
.job-error h4,
.job-traceback h4 {
  margin-bottom: 10px;
}
</style>
