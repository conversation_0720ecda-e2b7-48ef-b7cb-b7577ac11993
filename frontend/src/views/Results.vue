<template>
  <div class="results">
    <div class="page-header">
      <h1>提取结果</h1>
      <p>查看和管理数据提取结果</p>
    </div>
    
    <!-- 搜索和过滤 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="taskFilter" placeholder="选择任务" @change="handleFilter">
            <el-option label="全部任务" value="" />
            <el-option
              v-for="task in availableTasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="结果状态" @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleFilter"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="refreshResults">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 结果列表 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="results"
        style="width: 100%"
      >
        <el-table-column prop="task_name" label="任务名称" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="record_count" label="记录数" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="file_size" label="数据大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button size="small" @click="viewResult(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button size="small" type="primary" @click="downloadResult(row, 'json')">
              <el-icon><Download /></el-icon>
              JSON
            </el-button>
            <el-button size="small" @click="downloadResult(row, 'csv')">
              <el-icon><Download /></el-icon>
              CSV
            </el-button>
            <el-button size="small" type="danger" @click="deleteResult(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 查看结果对话框 -->
    <el-dialog
      v-model="showViewDialog"
      title="查看提取结果"
      width="80%"
      top="5vh"
    >
      <div v-if="selectedResult">
        <el-descriptions :column="2" border class="result-info">
          <el-descriptions-item label="任务名称">
            {{ selectedResult.task_name }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedResult.status === 'success' ? 'success' : 'danger'">
              {{ selectedResult.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="记录数">
            {{ selectedResult.record_count }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedResult.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="result-data">
          <h4>提取的数据:</h4>
          <el-input
            v-model="formattedResultData"
            type="textarea"
            :rows="20"
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import api from '@/utils/api'

const userStore = useUserStore()
const loading = ref(false)
const results = ref([])
const availableTasks = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

const taskFilter = ref('')
const statusFilter = ref('')
const dateRange = ref([])

const showViewDialog = ref(false)
const selectedResult = ref(null)

const formattedResultData = computed(() => {
  if (!selectedResult.value?.extracted_data) return ''
  return JSON.stringify(selectedResult.value.extracted_data, null, 2)
})

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatFileSize = (size) => {
  if (!size) return '-'
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const loadResults = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }
    
    if (taskFilter.value) {
      params.task_id = taskFilter.value
    }
    
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    
    const response = await api.get('/results/', { params })
    results.value = response.data.results
    total.value = response.data.pagination.total
  } catch (error) {
    ElMessage.error('加载结果列表失败')
  } finally {
    loading.value = false
  }
}

const loadTasks = async () => {
  try {
    const response = await api.get('/tasks/')
    availableTasks.value = response.data.tasks
  } catch (error) {
    console.error('Failed to load tasks:', error)
  }
}

const handleFilter = () => {
  currentPage.value = 1
  loadResults()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadResults()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadResults()
}

const refreshResults = () => {
  loadResults()
}

const viewResult = async (result) => {
  try {
    const response = await api.get(`/results/${result.id}`)
    selectedResult.value = response.data
    showViewDialog.value = true
  } catch (error) {
    ElMessage.error('加载结果详情失败')
  }
}

const downloadResult = async (result, format) => {
  try {
    const response = await api.get(`/results/${result.id}/download`, {
      params: { format },
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `result_${result.id}.${format}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('下载开始')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const deleteResult = async (result) => {
  try {
    await ElMessageBox.confirm('确定要删除这个结果吗？', '确认删除', {
      type: 'warning'
    })
    
    await api.delete(`/results/${result.id}`)
    ElMessage.success('结果删除成功')
    loadResults()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除结果失败')
    }
  }
}

onMounted(() => {
  userStore.initializeAuth()
  loadResults()
  loadTasks()
})
</script>

<style scoped>
.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.result-info {
  margin-bottom: 20px;
}

.result-data h4 {
  margin-bottom: 10px;
}
</style>
