<template>
  <div class="task-manager">
    <div class="header">
      <h2>任务管理</h2>
      <div class="header-actions">
        <div class="connection-status">
          <span class="status-indicator" :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
            <i class="icon-connection"></i>
            {{ isConnected ? '实时连接' : '连接断开' }}
          </span>
        </div>
        <button @click="showCreateModal = true" class="btn-primary">
          <i class="icon-plus"></i> 创建任务
        </button>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <div v-if="loading" class="loading">加载中...</div>
      
      <div v-else-if="tasks.length === 0" class="empty-state">
        <p>暂无任务</p>
      </div>

      <div v-else class="task-grid">
        <div 
          v-for="task in tasks" 
          :key="task.id" 
          class="task-card"
          :class="{ 'active': task.status === 'active' }"
        >
          <div class="task-header">
            <h3>{{ task.name }}</h3>
            <div class="task-status" :class="`status-${task.status}`">
              {{ getStatusText(task.status) }}
            </div>
          </div>

          <div class="task-info">
            <p class="task-url">{{ task.url }}</p>
            <p class="task-meta">
              创建时间: {{ formatDate(task.created_at) }}
            </p>
          </div>

          <div class="task-actions">
            <button 
              @click="executeTask(task.id)" 
              class="btn-execute"
              :disabled="task.status === 'running'"
            >
              {{ task.status === 'running' ? '执行中...' : '立即执行' }}
            </button>
            <button @click="editTask(task)" class="btn-edit">编辑</button>
            <button @click="deleteTask(task.id)" class="btn-delete">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.pages > 1" class="pagination">
      <button 
        @click="loadTasks(pagination.page - 1)"
        :disabled="pagination.page <= 1"
        class="btn-page"
      >
        上一页
      </button>
      
      <span class="page-info">
        第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页
      </span>
      
      <button 
        @click="loadTasks(pagination.page + 1)"
        :disabled="pagination.page >= pagination.pages"
        class="btn-page"
      >
        下一页
      </button>
    </div>

    <!-- 创建/编辑任务模态框 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ editingTask ? '编辑任务' : '创建任务' }}</h3>
          <button @click="closeModal" class="btn-close">&times;</button>
        </div>

        <form @submit.prevent="saveTask" class="task-form">
          <div class="form-group">
            <label>任务名称</label>
            <input 
              v-model="taskForm.name" 
              type="text" 
              required 
              placeholder="输入任务名称"
            >
          </div>

          <div class="form-group">
            <label>目标URL</label>
            <input 
              v-model="taskForm.url" 
              type="url" 
              required 
              placeholder="https://example.com"
            >
          </div>

          <div class="form-group">
            <label>提取规则 (JSON)</label>
            <textarea 
              v-model="taskForm.extraction_rules_json" 
              rows="6"
              placeholder='{"table_data": {"type": "table", "selector": "table"}}'
            ></textarea>
          </div>

          <div class="form-group">
            <label>认证配置 (可选)</label>
            <textarea 
              v-model="taskForm.auth_config_json" 
              rows="3"
              placeholder='{"type": "form", "username": "user", "password": "pass"}'
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="button" @click="closeModal" class="btn-cancel">
              取消
            </button>
            <button type="submit" class="btn-save" :disabled="saving">
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useTaskStore } from '../stores/taskStore'
import { formatDate } from '../utils/date'
import wsClient from '../utils/websocket'

export default {
  name: 'TaskManager',
  setup() {
    const taskStore = useTaskStore()
    
    const loading = ref(false)
    const saving = ref(false)
    const showCreateModal = ref(false)
    const editingTask = ref(null)
    
    const tasks = ref([])
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0,
      pages: 0
    })

    const taskForm = reactive({
      name: '',
      url: '',
      extraction_rules_json: '',
      auth_config_json: ''
    })
    
    // WebSocket连接状态
    const isConnected = wsClient.isConnected

    const loadTasks = async (page = 1) => {
      loading.value = true
      try {
        const response = await taskStore.fetchTasks({
          page,
          limit: pagination.limit
        })
        
        tasks.value = response.tasks
        Object.assign(pagination, response.pagination)
      } catch (error) {
        console.error('Failed to load tasks:', error)
      } finally {
        loading.value = false
      }
    }

    const executeTask = async (taskId) => {
      try {
        await taskStore.executeTask(taskId)
        // 更新任务状态
        const task = tasks.value.find(t => t.id === taskId)
        if (task) {
          task.status = 'queued'
        }
        
        // 订阅任务状态更新
        if (wsClient.isConnected.value) {
          wsClient.subscribeToTask(taskId)
        }
      } catch (error) {
        console.error('Failed to execute task:', error)
        alert('执行任务失败: ' + error.message)
      }
    }

    const editTask = (task) => {
      editingTask.value = task
      taskForm.name = task.name
      taskForm.url = task.url
      taskForm.extraction_rules_json = JSON.stringify(task.extraction_rules, null, 2)
      taskForm.auth_config_json = task.auth_config ? 
        JSON.stringify(task.auth_config, null, 2) : ''
      showCreateModal.value = true
    }

    const deleteTask = async (taskId) => {
      if (!confirm('确定要删除这个任务吗？')) return
      
      try {
        await taskStore.deleteTask(taskId)
        tasks.value = tasks.value.filter(t => t.id !== taskId)
      } catch (error) {
        console.error('Failed to delete task:', error)
        alert('删除任务失败: ' + error.message)
      }
    }

    const saveTask = async () => {
      saving.value = true
      try {
        const taskData = {
          name: taskForm.name,
          url: taskForm.url,
          extraction_rules: JSON.parse(taskForm.extraction_rules_json || '{}'),
          auth_config: taskForm.auth_config_json ? 
            JSON.parse(taskForm.auth_config_json) : null
        }

        if (editingTask.value) {
          await taskStore.updateTask(editingTask.value.id, taskData)
        } else {
          await taskStore.createTask(taskData)
        }

        closeModal()
        loadTasks(pagination.page)
      } catch (error) {
        console.error('Failed to save task:', error)
        alert('保存任务失败: ' + error.message)
      } finally {
        saving.value = false
      }
    }

    const closeModal = () => {
      showCreateModal.value = false
      editingTask.value = null
      Object.assign(taskForm, {
        name: '',
        url: '',
        extraction_rules_json: '',
        auth_config_json: ''
      })
    }

    const getStatusText = (status) => {
      const statusMap = {
        'active': '活跃',
        'inactive': '非活跃',
        'running': '运行中',
        'queued': '队列中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    }

    // WebSocket事件监听器
    const handleTaskStatusUpdate = (message) => {
      const { task_id, status, details } = message
      const task = tasks.value.find(t => t.id === task_id)
      if (task) {
        task.status = status
        if (details?.message) {
          task.last_message = details.message
        }
      }
    }

    const handleJobProgressUpdate = (message) => {
      const { task_id, progress, message: progressMessage } = message
      const task = tasks.value.find(t => t.id === task_id)
      if (task) {
        task.progress = progress
        task.progress_message = progressMessage
      }
    }

    const handleExtractionCompleted = (message) => {
      const { task_id, record_count, success } = message
      const task = tasks.value.find(t => t.id === task_id)
      if (task) {
        task.status = success ? 'completed' : 'failed'
        task.last_record_count = record_count
      }
      // 刷新任务列表以获取最新数据
      loadTasks(pagination.page)
    }

    onMounted(() => {
      loadTasks()
      
      // 连接WebSocket（使用模拟用户ID，实际应该从用户状态获取）
      const userId = 'user_' + Math.random().toString(36).substr(2, 9)
      wsClient.connect(userId)
      
      // 注册事件监听器
      wsClient.on('task_status_update', handleTaskStatusUpdate)
      wsClient.on('job_progress_update', handleJobProgressUpdate)
      wsClient.on('extraction_completed', handleExtractionCompleted)
    })

    onUnmounted(() => {
      // 清理WebSocket连接和事件监听器
      wsClient.off('task_status_update', handleTaskStatusUpdate)
      wsClient.off('job_progress_update', handleJobProgressUpdate)
      wsClient.off('extraction_completed', handleExtractionCompleted)
      wsClient.disconnect()
    })

    return {
      loading,
      saving,
      showCreateModal,
      editingTask,
      tasks,
      pagination,
      taskForm,
      isConnected,
      loadTasks,
      executeTask,
      editTask,
      deleteTask,
      saveTask,
      closeModal,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
.task-manager {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.connected {
  background-color: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.status-indicator.disconnected {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.icon-connection::before {
  content: '🔗';
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.task-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: white;
}

.task-card.active {
  border-color: #007bff;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.task-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-active { background: #d4edda; color: #155724; }
.status-running { background: #fff3cd; color: #856404; }
.status-failed { background: #f8d7da; color: #721c24; }

.task-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-execute { background: #28a745; color: white; }
.btn-edit { background: #ffc107; color: black; }
.btn-delete { background: #dc3545; color: white; }

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ddd;
}

.task-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.btn-cancel { background: #6c757d; color: white; }
.btn-save { background: #007bff; color: white; }

button {
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
