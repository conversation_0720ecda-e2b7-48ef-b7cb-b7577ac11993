<template>
  <div class="monitoring-dashboard">
    <div class="dashboard-header">
      <h2>系统监控</h2>
      <div class="header-actions">
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          @change="toggleAutoRefresh"
        />
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon cpu">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemMetrics.cpu_percent || 0 }}%</div>
              <div class="status-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon memory">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemMetrics.memory_percent || 0 }}%</div>
              <div class="status-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon disk">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ systemMetrics.disk_percent || 0 }}%</div>
              <div class="status-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon tasks">
              <el-icon><List /></el-icon>
            </div>
            <div class="status-info">
              <div class="status-value">{{ appMetrics.active_tasks || 0 }}</div>
              <div class="status-label">活跃任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <DataChart
          title="系统资源使用趋势"
          :data="resourceTrendData"
          type="line"
          :height="300"
        />
      </el-col>

      <el-col :span="12">
        <DataChart
          title="任务执行统计"
          :data="taskStatsData"
          type="pie"
          :height="300"
        />
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="24">
        <DataChart
          title="性能指标历史"
          :data="performanceHistoryData"
          type="line"
          :height="400"
          :show-table="true"
        />
      </el-col>
    </el-row>

    <!-- 告警信息 -->
    <el-card class="alerts-card">
      <template #header>
        <div class="card-header">
          <span>活跃告警</span>
          <el-badge :value="activeAlerts.length" class="alert-badge" />
        </div>
      </template>

      <div v-if="activeAlerts.length === 0" class="no-alerts">
        <el-icon><SuccessFilled /></el-icon>
        <span>系统运行正常，无活跃告警</span>
      </div>

      <div v-else class="alerts-list">
        <el-alert
          v-for="alert in activeAlerts"
          :key="alert.id"
          :title="alert.title"
          :description="alert.message"
          :type="getAlertType(alert.level)"
          :closable="false"
          show-icon
          class="alert-item"
        >
          <template #default>
            <div class="alert-content">
              <div class="alert-info">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-message">{{ alert.message }}</div>
                <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
              </div>
              <div class="alert-actions">
                <el-button size="small" @click="resolveAlert(alert.id)">
                  解决
                </el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Monitor,
  Cpu,
  FolderOpened,
  List,
  SuccessFilled
} from '@element-plus/icons-vue'
import DataChart from './DataChart.vue'
import { monitoringApi } from '@/api/monitoring'

const loading = ref(false)
const autoRefresh = ref(true)
let refreshTimer = null

// 系统指标
const systemMetrics = reactive({
  cpu_percent: 0,
  memory_percent: 0,
  disk_percent: 0
})

// 应用指标
const appMetrics = reactive({
  active_tasks: 0,
  completed_tasks: 0,
  failed_tasks: 0,
  error_rate: 0
})

// 图表数据
const resourceTrendData = ref([])
const taskStatsData = ref([])
const performanceHistoryData = ref([])

// 告警数据
const activeAlerts = ref([])

// 获取监控数据
const fetchMonitoringData = async () => {
  try {
    loading.value = true

    // 获取当前指标
    const metricsResponse = await monitoringApi.getCurrentMetrics()
    if (metricsResponse.success && metricsResponse.data) {
      const data = metricsResponse.data
      if (data.system) {
        Object.assign(systemMetrics, data.system)
      }
      if (data.application) {
        Object.assign(appMetrics, data.application)
      }
    }

    // 获取历史数据
    const historyResponse = await monitoringApi.getMetricsHistory(60)
    if (historyResponse.success && historyResponse.data) {
      updateChartData(historyResponse.data)
    }

    // 获取活跃告警
    const alertsResponse = await monitoringApi.getActiveAlerts()
    if (alertsResponse.success) {
      activeAlerts.value = alertsResponse.data || []
    }

  } catch (error) {
    console.error('获取监控数据失败:', error)
    ElMessage.error('获取监控数据失败')
  } finally {
    loading.value = false
  }
}

// 更新图表数据
const updateChartData = (historyData) => {
  if (!historyData || historyData.length === 0) return

  // 资源使用趋势
  resourceTrendData.value = historyData.slice(-20).map((item, index) => ({
    name: `${index + 1}`,
    value: item.system?.cpu_percent || 0
  }))

  // 任务统计
  const latestApp = historyData[historyData.length - 1]?.application
  if (latestApp) {
    taskStatsData.value = [
      { name: '已完成', value: latestApp.completed_tasks || 0 },
      { name: '失败', value: latestApp.failed_tasks || 0 },
      { name: '活跃', value: latestApp.active_tasks || 0 }
    ]
  }

  // 性能历史
  performanceHistoryData.value = historyData.slice(-50).map((item, index) => ({
    name: formatTime(item.system?.timestamp),
    value: item.system?.cpu_percent || 0,
    memory: item.system?.memory_percent || 0,
    disk: item.system?.disk_percent || 0
  }))
}

// 刷新数据
const refreshData = () => {
  fetchMonitoringData()
}

// 切换自动刷新
const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = setInterval(() => {
    fetchMonitoringData()
  }, 30000) // 30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 解决告警
const resolveAlert = async (alertId) => {
  try {
    await monitoringApi.resolveAlert(alertId)
    ElMessage.success('告警已解决')
    fetchMonitoringData()
  } catch (error) {
    console.error('解决告警失败:', error)
    ElMessage.error('解决告警失败')
  }
}

// 获取告警类型
const getAlertType = (level) => {
  const typeMap = {
    info: 'info',
    warning: 'warning',
    error: 'error',
    critical: 'error'
  }
  return typeMap[level] || 'info'
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString()
}

onMounted(() => {
  fetchMonitoringData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.monitoring-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.status-icon.cpu { background: #409EFF; }
.status-icon.memory { background: #67C23A; }
.status-icon.disk { background: #E6A23C; }
.status-icon.tasks { background: #F56C6C; }

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.charts-row {
  margin-bottom: 20px;
}

.alerts-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.no-alerts {
  text-align: center;
  padding: 40px;
  color: #67C23A;
}

.no-alerts .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  margin-bottom: 10px;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-info {
  flex: 1;
}

.alert-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.alert-message {
  color: #606266;
  margin-bottom: 5px;
}

.alert-time {
  font-size: 12px;
  color: #909399;
}

.alert-actions {
  margin-left: 20px;
}
</style>
