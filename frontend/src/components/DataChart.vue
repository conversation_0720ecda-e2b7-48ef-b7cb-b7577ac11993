<template>
  <div class="data-chart">
    <el-card class="chart-card">
      <template #header>
        <div class="chart-header">
          <span>{{ title }}</span>
          <div class="chart-controls">
            <el-select v-model="chartType" @change="updateChart" size="small">
              <el-option label="柱状图" value="bar" />
              <el-option label="折线图" value="line" />
              <el-option label="饼图" value="pie" />
              <el-option label="散点图" value="scatter" />
            </el-select>
            <el-button @click="exportChart" size="small" type="primary">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <div class="chart-container">
        <v-chart
          ref="chart"
          :option="chartOption"
          :style="{ height: chartHeight + 'px' }"
          autoresize
        />
      </div>

      <!-- 数据表格 -->
      <div v-if="showDataTable" class="data-table">
        <el-divider>数据详情</el-divider>
        <el-table :data="tableData" stripe size="small" max-height="300">
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
          />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, LineChart, PieChart, ScatterChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Download } from '@element-plus/icons-vue'

// 注册ECharts组件
use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
])

const props = defineProps({
  title: {
    type: String,
    default: '数据图表'
  },
  data: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'bar'
  },
  height: {
    type: Number,
    default: 400
  },
  showTable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['chart-ready', 'data-zoom'])

const chart = ref(null)
const chartType = ref(props.type)
const chartHeight = ref(props.height)
const showDataTable = ref(props.showTable)

// 图表配置
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999'
        }
      }
    }
  }

  const baseOption = {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }

  switch (chartType.value) {
    case 'bar':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.name || item.label)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'bar',
          data: props.data.map(item => item.value),
          itemStyle: {
            color: '#409EFF'
          }
        }]
      }

    case 'line':
      return {
        ...baseOption,
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.name || item.label)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'line',
          data: props.data.map(item => item.value),
          smooth: true,
          itemStyle: {
            color: '#67C23A'
          }
        }]
      }

    case 'pie':
      return {
        ...baseOption,
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: props.title,
          type: 'pie',
          radius: '50%',
          data: props.data.map(item => ({
            name: item.name || item.label,
            value: item.value
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }

    case 'scatter':
      return {
        ...baseOption,
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: 'scatter',
          data: props.data.map(item => [item.x || item.value, item.y || item.value]),
          itemStyle: {
            color: '#E6A23C'
          }
        }]
      }

    default:
      return baseOption
  }
})

// 表格数据
const tableData = computed(() => {
  return props.data.map((item, index) => ({
    index: index + 1,
    name: item.name || item.label || `项目${index + 1}`,
    value: item.value,
    ...item
  }))
})

const tableColumns = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  const columns = [
    { prop: 'index', label: '序号', width: 80 },
    { prop: 'name', label: '名称', width: 150 },
    { prop: 'value', label: '数值', width: 120 }
  ]

  // 动态添加其他字段
  const firstItem = props.data[0]
  Object.keys(firstItem).forEach(key => {
    if (!['name', 'label', 'value'].includes(key)) {
      columns.push({
        prop: key,
        label: key,
        width: 120
      })
    }
  })

  return columns
})

// 更新图表
const updateChart = () => {
  if (chart.value) {
    chart.value.setOption(chartOption.value, true)
  }
}

// 导出图表
const exportChart = () => {
  if (chart.value) {
    const url = chart.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${props.title}_${new Date().getTime()}.png`
    link.href = url
    link.click()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.type, (newType) => {
  chartType.value = newType
  updateChart()
})

onMounted(() => {
  emit('chart-ready', chart.value)
})
</script>

<style scoped>
.data-chart {
  width: 100%;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.chart-container {
  width: 100%;
  min-height: 300px;
}

.data-table {
  margin-top: 20px;
}

.el-divider {
  margin: 20px 0 10px 0;
}
</style>
