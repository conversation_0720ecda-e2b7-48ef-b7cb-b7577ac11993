import { ref, reactive } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

class WebSocketClient {
  constructor() {
    this.ws = null
    this.isConnected = ref(false)
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.heartbeatInterval = 30000
    this.heartbeatTimer = null
    this.reconnectTimer = null
    
    // 事件监听器
    this.listeners = {
      'task_status_update': [],
      'job_progress_update': [],
      'extraction_completed': [],
      'system_alert': [],
      'connection_established': [],
      'error': []
    }
    
    // 连接状态
    this.connectionStats = reactive({
      total_connections: 0,
      total_subscriptions: 0,
      active_tasks: 0,
      connected_users: []
    })
  }
  
  /**
   * 连接WebSocket
   * @param {string} userId - 用户ID
   * @param {string} token - 认证令牌（可选）
   */
  connect(userId, token = null) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected')
      return
    }
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    // 使用后端服务器地址而不是前端地址
    const host = window.location.hostname + ':8000'
    let wsUrl = `${protocol}//${host}/api/v1/ws/${userId}`
    
    if (token) {
      wsUrl += `?token=${token}`
    }
    
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.isConnected.value = true
        this.reconnectAttempts = 0
        this.startHeartbeat()
        this.emit('connection_established', { connected: true })
        
        ElMessage.success('实时连接已建立')
      }
      
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        this.isConnected.value = false
        this.stopHeartbeat()
        
        if (event.code !== 1000) { // 非正常关闭
          this.attemptReconnect(userId, token)
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        this.emit('error', { error: 'WebSocket连接错误' })
      }
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      ElMessage.error('无法建立实时连接')
    }
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    
    this.isConnected.value = false
  }
  
  /**
   * 发送消息
   * @param {object} message - 要发送的消息
   */
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }
  
  /**
   * 订阅任务状态更新
   * @param {string} taskId - 任务ID
   */
  subscribeToTask(taskId) {
    this.send({
      type: 'subscribe_task',
      task_id: taskId
    })
  }
  
  /**
   * 取消订阅任务状态更新
   * @param {string} taskId - 任务ID
   */
  unsubscribeFromTask(taskId) {
    this.send({
      type: 'unsubscribe_task',
      task_id: taskId
    })
  }
  
  /**
   * 获取连接统计信息
   */
  getStats() {
    this.send({
      type: 'get_stats'
    })
  }
  
  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数
   */
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }
  
  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {function} callback - 回调函数
   */
  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {object} data - 事件数据
   */
  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in ${event} listener:`, error)
        }
      })
    }
  }
  
  /**
   * 处理接收到的消息
   * @param {object} message - 消息对象
   */
  handleMessage(message) {
    const { type } = message
    
    switch (type) {
      case 'connection_established':
        console.log('WebSocket connection established:', message)
        this.emit('connection_established', message)
        break
        
      case 'task_status_update':
        console.log('Task status update:', message)
        this.emit('task_status_update', message)
        this.showTaskStatusNotification(message)
        break
        
      case 'job_progress_update':
        console.log('Job progress update:', message)
        this.emit('job_progress_update', message)
        break
        
      case 'extraction_completed':
        console.log('Extraction completed:', message)
        this.emit('extraction_completed', message)
        this.showExtractionNotification(message)
        break
        
      case 'system_alert':
        console.log('System alert:', message)
        this.emit('system_alert', message)
        this.showSystemAlert(message)
        break
        
      case 'connection_stats':
        Object.assign(this.connectionStats, message.stats)
        break
        
      case 'subscription_confirmed':
      case 'unsubscription_confirmed':
        ElMessage.success(message.message)
        break
        
      case 'pong':
        // 心跳响应，不需要特殊处理
        break
        
      case 'error':
        console.error('WebSocket error message:', message)
        this.emit('error', message)
        ElMessage.error(message.message || '发生未知错误')
        break
        
      default:
        console.log('Unknown message type:', type, message)
    }
  }
  
  /**
   * 显示任务状态通知
   * @param {object} message - 任务状态消息
   */
  showTaskStatusNotification(message) {
    const { task_id, status, details } = message
    
    let title = '任务状态更新'
    let type = 'info'
    
    switch (status) {
      case 'queued':
        title = '任务已排队'
        type = 'info'
        break
      case 'running':
        title = '任务执行中'
        type = 'warning'
        break
      case 'completed':
        title = '任务完成'
        type = 'success'
        break
      case 'failed':
        title = '任务失败'
        type = 'error'
        break
    }
    
    ElNotification({
      title,
      message: details?.message || `任务 ${task_id} 状态: ${status}`,
      type,
      duration: 4000
    })
  }
  
  /**
   * 显示数据提取通知
   * @param {object} message - 提取完成消息
   */
  showExtractionNotification(message) {
    const { task_id, record_count, success } = message
    
    ElNotification({
      title: success ? '数据提取完成' : '数据提取失败',
      message: success 
        ? `任务 ${task_id} 成功提取 ${record_count} 条记录`
        : `任务 ${task_id} 提取失败`,
      type: success ? 'success' : 'error',
      duration: 5000
    })
  }
  
  /**
   * 显示系统告警
   * @param {object} message - 系统告警消息
   */
  showSystemAlert(message) {
    const { alert_type, message: alertMessage, severity } = message
    
    let type = 'info'
    switch (severity) {
      case 'error':
        type = 'error'
        break
      case 'warning':
        type = 'warning'
        break
      case 'success':
        type = 'success'
        break
    }
    
    ElNotification({
      title: `系统告警: ${alert_type}`,
      message: alertMessage,
      type,
      duration: 0 // 不自动关闭
    })
  }
  
  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.send({
        type: 'ping',
        timestamp: new Date().toISOString()
      })
    }, this.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  /**
   * 尝试重连
   * @param {string} userId - 用户ID
   * @param {string} token - 认证令牌
   */
  attemptReconnect(userId, token) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached')
      ElMessage.error('无法重新连接到服务器')
      return
    }
    
    this.reconnectAttempts++
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect(userId, token)
    }, this.reconnectInterval)
  }
}

// 创建全局WebSocket客户端实例
const wsClient = new WebSocketClient()

export default wsClient
export { WebSocketClient, wsClient }