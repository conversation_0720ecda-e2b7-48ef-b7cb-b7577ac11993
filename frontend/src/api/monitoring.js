/**
 * 监控相关API
 */
import request from './request'

export const monitoringApi = {
  /**
   * 获取当前监控指标
   */
  getCurrentMetrics() {
    return request.get('/api/v1/dashboard/monitoring/metrics')
  },

  /**
   * 获取监控历史数据
   * @param {number} minutes - 获取多少分钟内的数据
   */
  getMetricsHistory(minutes = 60) {
    return request.get('/api/v1/dashboard/monitoring/history', {
      params: { minutes }
    })
  },

  /**
   * 获取活跃告警
   */
  getActiveAlerts() {
    return request.get('/api/v1/dashboard/monitoring/alerts')
  },

  /**
   * 获取告警历史
   * @param {number} hours - 获取多少小时内的告警
   */
  getAlertHistory(hours = 24) {
    return request.get('/api/v1/dashboard/monitoring/alerts/history', {
      params: { hours }
    })
  },

  /**
   * 解决告警
   * @param {string} alertId - 告警ID
   */
  resolveAlert(alertId) {
    return request.post(`/api/v1/dashboard/monitoring/alerts/${alertId}/resolve`)
  },

  /**
   * 获取系统性能指标
   */
  getSystemPerformance() {
    return request.get('/api/v1/performance/system')
  },

  /**
   * 获取应用性能指标
   */
  getAppPerformance() {
    return request.get('/api/v1/performance/app')
  }
}

export default monitoringApi
