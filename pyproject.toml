[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[project]
name = "loop-hole"
version = "1.0.0"
description = "智能网页数据提取系统"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "Loop Hole Team"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "fastapi==0.110.0",
    "starlette==0.36.3",
    "uvicorn[standard]==0.24.0",
    "celery==5.3.4",
    "redis==5.0.1",
    "sqlalchemy==2.0.23",
    "alembic==1.12.1",
    "playwright==1.40.0",
    "beautifulsoup4>=4.12.2",
    "pandas>=2.1.3",
    "numpy>=1.26.0,<2.0.0",
    "scikit-learn>=1.3.2",
    "pydantic==2.5.0",
    "pydantic-settings",
    "python-jose[cryptography]==3.3.0",
    "PyJWT==2.8.0",
    "python-multipart==0.0.6",
    "structlog==23.2.0",
    "python-json-logger==2.0.7",
    "prometheus-client==0.19.0",
    "psutil==5.9.6",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "httpx==0.27.0",
    "black==23.11.0",
    "isort==5.12.0",
    "pylint==3.0.3",
]
postgres = [
    "psycopg2-binary==2.9.9",
]

[project.urls]
Homepage = "https://github.com/your-org/loop-hole"
Repository = "https://github.com/your-org/loop-hole.git"
Issues = "https://github.com/your-org/loop-hole/issues"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pylint.messages_control]
disable = "C0330, C0326"

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q"
testpaths = [
    "tests",
]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"
