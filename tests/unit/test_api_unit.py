"""
Loop Hole API 测试脚本 - Pytest 版本
用于验证核心功能是否正常工作
"""
import os
import sys
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 将项目根目录添加到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Base
from app.models.user import User
from app.models.task import ExtractionTask
from app.api.v1.auth import hash_password, create_access_token, verify_password
from app.core.analyzer import PageAnalyzer

# --- Fixtures ---

@pytest.fixture(scope="module")
def db_session():
    """创建一个独立的测试数据库会话，并在测试结束后清理。"""
    DATABASE_URL = "sqlite:///./test_api.db"
    engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)
        if os.path.exists("./test_loop_hole.db"):
            os.remove("./test_loop_hole.db")

@pytest.fixture(scope="module")
def test_user(db_session):
    """创建一个测试用户并存入数据库。"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password=hash_password("testpassword"),
        full_name="Test User",
        role="user"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

# --- Tests ---

def test_database_connection(db_session):
    """测试数据库连接和基本查询。"""
    print("🔍 测试数据库连接...")
    users_count = db_session.query(User).count()
    assert users_count >= 0
    print(f"✅ 数据库连接成功，初始用户数: {users_count}")

def test_user_model_creation(test_user):
    """测试用户模型是否成功创建。"""
    print("\n🔍 测试用户模型...")
    assert test_user.id is not None
    assert test_user.username == "testuser"
    assert test_user.email == "<EMAIL>"
    print(f"✅ 用户模型测试成功，用户ID: {test_user.id}")

def test_task_model_creation(db_session, test_user):
    """测试任务模型的创建。"""
    print("\n🔍 测试任务模型...")
    task = ExtractionTask(
        name="测试任务",
        url="https://example.com",
        extraction_rules={"test": "rule"},
        created_by=test_user.id,
        total_runs=0
    )
    db_session.add(task)
    db_session.commit()
    db_session.refresh(task)

    retrieved_task = db_session.query(ExtractionTask).filter(ExtractionTask.name == "测试任务").first()
    assert retrieved_task is not None
    assert retrieved_task.url == "https://example.com"
    assert retrieved_task.created_by == test_user.id
    print(f"✅ 任务模型测试成功，任务ID: {retrieved_task.id}")

def test_core_page_analyzer():
    """测试核心组件 PageAnalyzer。"""
    print("\n🔍 测试核心组件 PageAnalyzer...")
    analyzer = PageAnalyzer()
    test_html = """
    <html>
        <body>
            <table>
                <tr><th>Name</th><th>Age</th></tr>
                <tr><td>John</td><td>25</td></tr>
                <tr><td>Jane</td><td>30</td></tr>
            </table>
        </body>
    </html>
    """
    result = analyzer.analyze_page(test_html, "https://test.com")
    assert len(result.tables) > 0
    assert result.confidence_score > 0
    print(f"✅ PageAnalyzer测试成功，检测到 {len(result.tables)} 个表格")

def test_auth_functions():
    """测试认证功能 (密码哈希和JWT)。"""
    print("\n🔍 测试认证功能...")
    # 测试密码哈希
    password = "testpassword123"
    hashed = hash_password(password)
    assert verify_password(password, hashed)
    assert not verify_password("wrongpassword", hashed)

    # 测试JWT令牌创建
    token_data = {"sub": "test-user-id", "username": "testuser"}
    token = create_access_token(token_data)
    assert isinstance(token, str)
    assert len(token) > 0
    print("✅ 认证功能测试成功")
