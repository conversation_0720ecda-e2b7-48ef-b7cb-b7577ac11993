#!/usr/bin/env python3
"""
内存缓存系统测试脚本

这个脚本测试我们新实现的内存缓存系统的功能，包括：
- 基本的set/get操作
- TTL过期机制
- 键存在性检查
- 模式匹配删除
- 线程安全性
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.cache import cache_manager, extraction_cache, session_cache, metrics_cache

def test_basic_operations():
    """测试基本的缓存操作"""
    print("\n=== 测试基本缓存操作 ===")

    # 测试set和get
    result = cache_manager.set("test_key", "test_value", ttl=60)
    print(f"Set operation result: {result}")

    value = cache_manager.get("test_key")
    print(f"Get operation result: {value}")

    # 测试exists
    exists = cache_manager.exists("test_key")
    print(f"Key exists: {exists}")

    # 测试delete
    deleted = cache_manager.delete("test_key")
    print(f"Delete operation result: {deleted}")

    # 验证删除后不存在
    exists_after_delete = cache_manager.exists("test_key")
    print(f"Key exists after delete: {exists_after_delete}")

def test_ttl_expiration():
    """测试TTL过期机制"""
    print("\n=== 测试TTL过期机制 ===")

    # 设置短TTL的缓存
    cache_manager.set("expire_test", "will_expire", ttl=2)
    print("Set key with 2 second TTL")

    # 立即检查
    value = cache_manager.get("expire_test")
    print(f"Immediate get: {value}")

    # 等待过期
    print("Waiting 3 seconds for expiration...")
    time.sleep(3)

    # 检查是否过期
    value_after_expire = cache_manager.get("expire_test")
    print(f"Get after expiration: {value_after_expire}")

def test_complex_data_types():
    """测试复杂数据类型的缓存"""
    print("\n=== 测试复杂数据类型 ===")

    # 测试字典
    test_dict = {
        "name": "test",
        "data": [1, 2, 3],
        "nested": {"key": "value"}
    }
    cache_manager.set("dict_test", test_dict, ttl=60)
    retrieved_dict = cache_manager.get("dict_test")
    print(f"Dict test - Original: {test_dict}")
    print(f"Dict test - Retrieved: {retrieved_dict}")
    print(f"Dict test - Equal: {test_dict == retrieved_dict}")

    # 测试列表
    test_list = ["a", "b", "c", 1, 2, 3]
    cache_manager.set("list_test", test_list, ttl=60)
    retrieved_list = cache_manager.get("list_test")
    print(f"List test - Original: {test_list}")
    print(f"List test - Retrieved: {retrieved_list}")
    print(f"List test - Equal: {test_list == retrieved_list}")

def test_pattern_operations():
    """测试模式匹配操作"""
    print("\n=== 测试模式匹配操作 ===")

    # 设置多个键
    test_keys = [
        "user:123:profile",
        "user:456:profile",
        "user:789:settings",
        "session:abc123",
        "session:def456"
    ]

    for key in test_keys:
        cache_manager.set(key, f"data_for_{key}", ttl=60)

    print(f"Set {len(test_keys)} test keys")

    # 测试模式清除
    deleted_count = cache_manager.clear_pattern("user:*:profile")
    print(f"Deleted {deleted_count} keys matching 'user:*:profile'")

    # 验证剩余的键
    remaining_keys = []
    for key in test_keys:
        if cache_manager.exists(key):
            remaining_keys.append(key)

    print(f"Remaining keys: {remaining_keys}")

def test_thread_safety():
    """测试线程安全性"""
    print("\n=== 测试线程安全性 ===")

    def worker(thread_id):
        """工作线程函数"""
        for i in range(10):
            key = f"thread_{thread_id}_key_{i}"
            value = f"thread_{thread_id}_value_{i}"
            cache_manager.set(key, value, ttl=60)
            retrieved = cache_manager.get(key)
            if retrieved != value:
                print(f"Thread safety issue in thread {thread_id}: expected {value}, got {retrieved}")
                return False
        return True

    # 使用多线程测试
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(worker, i) for i in range(5)]
        results = [future.result() for future in futures]

    if all(results):
        print("Thread safety test passed!")
    else:
        print("Thread safety test failed!")

def test_high_level_caches():
    """测试高级缓存类"""
    print("\n=== 测试高级缓存类 ===")

    # 测试extraction_cache
    extraction_result = {
        "data": [{"title": "Test", "content": "Content"}],
        "count": 1
    }
    extraction_rules = {"title": ".title", "content": ".content"}

    success = extraction_cache.cache_extraction_result(
        url="https://example.com",
        extraction_rules=extraction_rules,
        result=extraction_result,
        ttl=60
    )
    print(f"Extraction cache set: {success}")

    cached_result = extraction_cache.get_cached_extraction(
        url="https://example.com",
        extraction_rules=extraction_rules
    )
    print(f"Extraction cache get: {cached_result is not None}")

    # 测试session_cache
    session_data = {
        "user_id": "123",
        "permissions": ["read", "write"],
        "last_activity": "2025-01-01T00:00:00Z"
    }

    session_success = session_cache.set_user_session(
        user_id="123",
        session_data=session_data,
        ttl=3600
    )
    print(f"Session cache set: {session_success}")

    cached_session = session_cache.get_user_session("123")
    print(f"Session cache get: {cached_session is not None}")

    # 测试metrics_cache
    metrics_data = {
        "cpu_usage": 45.2,
        "memory_usage": 67.8,
        "active_tasks": 5
    }

    metrics_success = metrics_cache.cache_system_metrics(metrics_data)
    print(f"Metrics cache set: {metrics_success}")

    cached_metrics = metrics_cache.get_system_metrics()
    print(f"Metrics cache get: {cached_metrics is not None}")

def main():
    """主测试函数"""
    print("🧪 内存缓存系统测试开始")
    print(f"缓存后端类型: {type(cache_manager._get_client()).__name__}")

    try:
        test_basic_operations()
        test_ttl_expiration()
        test_complex_data_types()
        test_pattern_operations()
        test_thread_safety()
        test_high_level_caches()

        print("\n✅ 所有测试完成！内存缓存系统工作正常。")
        print("\n📝 迁移到Redis的步骤：")
        print("1. 安装并启动Redis服务")
        print("2. 设置环境变量 USE_MEMORY_CACHE=false")
        print("3. 配置REDIS_URL环境变量（可选，默认为redis://localhost:6379/0）")
        print("4. 重启应用，系统将自动切换到Redis")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
