@echo off
setlocal enabledelayedexpansion

REM Loop Hole Windows 快速启动脚本
REM 此脚本将自动部署和启动 Loop Hole 智能网页数据提取系统

title Loop Hole - 快速启动

REM 颜色定义 (Windows 10+ 支持ANSI颜色)
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

REM 显示横幅
:show_banner
echo %BLUE%
echo  _                       _   _       _
echo ^| ^|                     ^| ^| ^| ^|     ^| ^|
echo ^| ^|     ___   ___  _ __ ^| ^|_^| ^| ___ ^| ^| ___
echo ^| ^|    / _ \ / _ \^| '_ \^|  _  ^|/ _ \^| ^|/ _ \
echo ^| ^|___^| ^(^_^) ^| ^(^_^) ^| ^|_^) ^| ^| ^| ^| ^(^_^) ^| ^|  __/
echo \_____/\___/ \___/^| .__/\_^| ^|_/\___/^|_^|\___^|
echo                   ^| ^|
echo                   ^|_^|
echo.
echo 智能网页数据提取系统 - Windows 快速启动脚本
echo %NC%
goto :eof

REM 检查命令是否存在
:check_command
where %1 >nul 2>&1
if %errorlevel% equ 0 (
    exit /b 0
) else (
    exit /b 1
)

REM 检查端口是否被占用
:check_port
netstat -an | findstr ":%1 " >nul 2>&1
if %errorlevel% equ 0 (
    exit /b 1
) else (
    exit /b 0
)

REM 等待服务启动
:wait_for_service
set "url=%~1"
set "service_name=%~2"
set "max_attempts=30"
set "attempt=1"

call :log_info "等待 %service_name% 启动..."

:wait_loop
if !attempt! gtr !max_attempts! (
    call :log_error "%service_name% 启动超时"
    exit /b 1
)

REM 使用curl测试服务是否可用
curl -s -f "%url%" >nul 2>&1
if %errorlevel% equ 0 (
    call :log_success "%service_name% 已启动"
    exit /b 0
)

echo|set /p="."
timeout /t 2 /nobreak >nul
set /a attempt+=1
goto wait_loop

REM 检查系统环境
:check_prerequisites
call :log_info "检查系统环境..."

REM 检查操作系统
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
call :log_info "检测到 Windows 版本: %VERSION%"

REM 检查Docker
call :check_command docker
if %errorlevel% neq 0 (
    call :log_error "Docker 未安装或不在 PATH 中"
    call :log_info "请先安装 Docker Desktop: https://www.docker.com/products/docker-desktop"
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('docker --version 2^>nul') do set DOCKER_VERSION=%%i
call :log_success "Docker 已安装: %DOCKER_VERSION%"

REM 检查Docker Compose
docker compose version >nul 2>&1
if %errorlevel% equ 0 (
    set "DOCKER_COMPOSE=docker compose"
    for /f "tokens=*" %%i in ('docker compose version --short 2^>nul') do set COMPOSE_VERSION=%%i
    call :log_success "Docker Compose 已安装: %COMPOSE_VERSION%"
) else (
    call :check_command docker-compose
    if !errorlevel! neq 0 (
        call :log_error "Docker Compose 未安装或不在 PATH 中"
        call :log_info "请先安装 Docker Compose"
        pause
        exit /b 1
    )
    set "DOCKER_COMPOSE=docker-compose"
    for /f "tokens=*" %%i in ('docker-compose --version 2^>nul') do set COMPOSE_VERSION=%%i
    call :log_success "Docker Compose 已安装: %COMPOSE_VERSION%"
)

REM 检查Git
call :check_command git
if %errorlevel% neq 0 (
    call :log_warning "Git 未安装，将跳过版本检查"
) else (
    for /f "tokens=*" %%i in ('git --version 2^>nul') do set GIT_VERSION=%%i
    call :log_success "Git 已安装: %GIT_VERSION%"
)

REM 检查端口占用
set "PORTS=80 8000 5432 6379"
set "OCCUPIED_PORTS="

for %%p in (%PORTS%) do (
    call :check_port %%p
    if !errorlevel! neq 0 (
        set "OCCUPIED_PORTS=!OCCUPIED_PORTS! %%p"
    )
)

if defined OCCUPIED_PORTS (
    call :log_warning "以下端口被占用:%OCCUPIED_PORTS%"
    call :log_warning "这可能会导致服务启动失败"
    set /p "response=是否继续? [y/N]: "
    if /i "!response!" neq "y" (
        call :log_info "已取消启动"
        pause
        exit /b 0
    )
)

call :log_success "环境检查完成"
goto :eof

REM 创建环境配置文件
:create_env_file
call :log_info "创建环境配置文件..."

if not exist .env (
    (
        echo # 数据库配置
        echo DATABASE_URL=*******************************************************/loop_hole
        echo.
        echo # Redis配置
        echo REDIS_URL=redis://redis:6379/0
        echo.
        echo # 应用配置
        echo SECRET_KEY=loop-hole-secret-key-change-in-production
        echo DEBUG=False
        echo ENVIRONMENT=production
        echo API_HOST=0.0.0.0
        echo API_PORT=8000
        echo.
        echo # Celery配置
        echo CELERY_BROKER_URL=redis://redis:6379/0
        echo CELERY_RESULT_BACKEND=redis://redis:6379/0
        echo CELERY_WORKER_CONCURRENCY=4
        echo.
        echo # 数据库配置
        echo POSTGRES_DB=loop_hole
        echo POSTGRES_USER=loop_hole
        echo POSTGRES_PASSWORD=loop_hole_password
        echo.
        echo # 前端配置
        echo VITE_API_BASE_URL=http://localhost:8000
    ) > .env
    call :log_success "已创建 .env 文件"
) else (
    call :log_info ".env 文件已存在，跳过创建"
)
goto :eof

REM 创建开发环境配置
:create_dev_compose
if not exist docker-compose.dev.yml (
    call :log_info "创建开发环境配置文件..."
    (
        echo version: '3.8'
        echo.
        echo services:
        echo   postgres:
        echo     image: postgres:13-alpine
        echo     environment:
        echo       - POSTGRES_DB=${POSTGRES_DB}
        echo       - POSTGRES_USER=${POSTGRES_USER}
        echo       - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
        echo     volumes:
        echo       - postgres_data:/var/lib/postgresql/data
        echo     ports:
        echo       - "5432:5432"
        echo     healthcheck:
        echo       test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
        echo       interval: 10s
        echo       timeout: 5s
        echo       retries: 5
        echo.
        echo   redis:
        echo     image: redis:6-alpine
        echo     ports:
        echo       - "6379:6379"
        echo     healthcheck:
        echo       test: ["CMD", "redis-cli", "ping"]
        echo       interval: 10s
        echo       timeout: 5s
        echo       retries: 5
        echo.
        echo   web:
        echo     build: .
        echo     ports:
        echo       - "8000:8000"
        echo     environment:
        echo       - DATABASE_URL=${DATABASE_URL}
        echo       - REDIS_URL=${REDIS_URL}
        echo       - SECRET_KEY=${SECRET_KEY}
        echo       - DEBUG=${DEBUG}
        echo       - ENVIRONMENT=${ENVIRONMENT}
        echo     volumes:
        echo       - ./app:/app/app
        echo     depends_on:
        echo       postgres:
        echo         condition: service_healthy
        echo       redis:
        echo         condition: service_healthy
        echo     healthcheck:
        echo       test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
        echo       interval: 30s
        echo       timeout: 10s
        echo       retries: 3
        echo     command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
        echo.
        echo   worker:
        echo     build: .
        echo     environment:
        echo       - DATABASE_URL=${DATABASE_URL}
        echo       - CELERY_BROKER_URL=${CELERY_BROKER_URL}
        echo       - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
        echo       - CELERY_WORKER_CONCURRENCY=${CELERY_WORKER_CONCURRENCY}
        echo     volumes:
        echo       - ./app:/app/app
        echo     depends_on:
        echo       postgres:
        echo         condition: service_healthy
        echo       redis:
        echo         condition: service_healthy
        echo     command: celery -A app.core.tasks worker --loglevel=info --concurrency=${CELERY_WORKER_CONCURRENCY}
        echo.
        echo   frontend:
        echo     build: ./frontend
        echo     ports:
        echo       - "80:80"
        echo     depends_on:
        echo       - web
        echo     environment:
        echo       - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        echo.
        echo volumes:
        echo   postgres_data:
    ) > docker-compose.dev.yml
    call :log_success "已创建开发环境配置文件"
)
goto :eof

REM 启动服务
:start_services
call :log_info "构建和启动服务..."

REM 拉取基础镜像
call :log_info "拉取基础镜像..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml pull postgres redis

REM 构建应用镜像
call :log_info "构建应用镜像..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml build

REM 启动服务
call :log_info "启动服务..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml up -d

REM 等待服务启动
call :log_info "等待服务启动完成..."
timeout /t 5 /nobreak >nul

REM 检查服务状态
call :log_info "检查服务状态..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml ps
goto :eof

REM 初始化数据库
:init_database
call :log_info "初始化数据库..."

REM 等待数据库准备就绪
call :wait_for_service "http://localhost:8000/api/v1/health" "后端API"
if %errorlevel% neq 0 exit /b 1

REM 运行数据库迁移
call :log_info "运行数据库迁移..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml exec web alembic upgrade head

call :log_success "数据库初始化完成"
goto :eof

REM 验证部署
:verify_deployment
call :log_info "验证部署状态..."

REM 检查后端API
call :wait_for_service "http://localhost:8000/api/v1/health" "后端API"
if %errorlevel% neq 0 (
    call :log_error "后端API启动失败"
    call :show_logs
    pause
    exit /b 1
)
call :log_success "后端API运行正常"

REM 检查前端
call :wait_for_service "http://localhost" "前端界面"
if %errorlevel% neq 0 (
    call :log_warning "前端界面可能需要更多时间启动"
) else (
    call :log_success "前端界面运行正常"
)

REM 检查数据库连接
call :log_info "检查数据库连接..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml exec postgres pg_isready -U loop_hole >nul 2>&1
if %errorlevel% equ 0 (
    call :log_success "数据库连接正常"
) else (
    call :log_error "数据库连接失败"
)

REM 检查Redis连接
call :log_info "检查Redis连接..."
%DOCKER_COMPOSE% -f docker-compose.dev.yml exec redis redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    call :log_success "Redis连接正常"
) else (
    call :log_error "Redis连接失败"
)

call :log_success "部署验证完成"
goto :eof

REM 显示访问信息
:show_access_info
echo.
call :log_success "🎉 Loop Hole 系统启动成功！"
echo.
echo %GREEN%访问地址:%NC%
echo   前端界面: %BLUE%http://localhost%NC%
echo   API文档:  %BLUE%http://localhost:8000/docs%NC%
echo   API接口: %BLUE%http://localhost:8000%NC%
echo.
echo %GREEN%常用命令:%NC%
echo   查看日志: %YELLOW%%DOCKER_COMPOSE% -f docker-compose.dev.yml logs -f%NC%
echo   停止服务: %YELLOW%%DOCKER_COMPOSE% -f docker-compose.dev.yml down%NC%
echo   重启服务: %YELLOW%%DOCKER_COMPOSE% -f docker-compose.dev.yml restart%NC%
echo   查看状态: %YELLOW%%DOCKER_COMPOSE% -f docker-compose.dev.yml ps%NC%
echo.
echo %GREEN%快速开始:%NC%
echo   1. 打开浏览器访问: %BLUE%http://localhost%NC%
echo   2. 点击 '任务管理' 创建第一个数据提取任务
echo   3. 输入目标网站URL，配置提取规则
echo   4. 点击 '测试提取' 验证配置
echo   5. 保存并执行任务
echo.
echo %GREEN%获取帮助:%NC%
echo   详细使用指南: %BLUE%docs\user_guide.md%NC%
echo   API文档: %BLUE%http://localhost:8000/docs%NC%
echo.
goto :eof

REM 显示错误日志
:show_logs
call :log_error "启动失败，显示最近日志："
echo ----------------------------------------
%DOCKER_COMPOSE% -f docker-compose.dev.yml logs --tail=20
echo ----------------------------------------
call :log_info "查看完整日志: %DOCKER_COMPOSE% -f docker-compose.dev.yml logs"
goto :eof

REM 主函数
:main
call :show_banner

REM 检查是否在项目目录中
if not exist requirements.txt (
    call :log_error "请在 Loop Hole 项目根目录中运行此脚本"
    call :log_info "当前目录: %CD%"
    call :log_info "请确保目录包含 requirements.txt 和 app\ 文件夹"
    pause
    exit /b 1
)

if not exist app (
    call :log_error "请在 Loop Hole 项目根目录中运行此脚本"
    call :log_info "当前目录: %CD%"
    call :log_info "请确保目录包含 requirements.txt 和 app\ 文件夹"
    pause
    exit /b 1
)

call :log_info "开始启动 Loop Hole 系统..."

REM 执行启动流程
call :check_prerequisites
if %errorlevel% neq 0 exit /b 1

call :create_env_file
call :create_dev_compose
call :start_services

REM 等待服务完全启动
call :log_info "等待服务完全启动 (30秒)..."
timeout /t 30 /nobreak >nul

call :init_database
if %errorlevel% neq 0 exit /b 1

call :verify_deployment
if %errorlevel% neq 0 exit /b 1

call :show_access_info

call :log_success "启动完成！系统已准备就绪。"

pause
goto :eof

REM 脚本选项处理
if "%~1"=="--help" goto show_help
if "%~1"=="-h" goto show_help
if "%~1"=="--logs" goto show_logs_cmd
if "%~1"=="--stop" goto stop_services
if "%~1"=="--restart" goto restart_services
if "%~1"=="--status" goto show_status
if "%~1"=="--clean" goto clean_all

REM 运行主函数
goto main

:show_help
echo Loop Hole Windows 快速启动脚本
echo.
echo 用法: %0 [选项]
echo.
echo 选项:
echo   -h, --help     显示此帮助信息
echo   --logs         显示服务日志
echo   --stop         停止所有服务
echo   --restart      重启所有服务
echo   --status       显示服务状态
echo   --clean        清理所有数据和容器
echo.
pause
exit /b 0

:show_logs_cmd
docker-compose -f docker-compose.dev.yml logs -f
exit /b 0

:stop_services
call :log_info "停止所有服务..."
docker-compose -f docker-compose.dev.yml down
call :log_success "服务已停止"
pause
exit /b 0

:restart_services
call :log_info "重启所有服务..."
docker-compose -f docker-compose.dev.yml restart
call :log_success "服务已重启"
pause
exit /b 0

:show_status
docker-compose -f docker-compose.dev.yml ps
pause
exit /b 0

:clean_all
set /p "response=确定要清理所有数据和容器吗? [y/N]: "
if /i "%response%"=="y" (
    call :log_info "清理所有数据和容器..."
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans
    docker system prune -f
    call :log_success "清理完成"
) else (
    call :log_info "已取消清理"
)
pause
exit /b 0
