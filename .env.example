# Loop Hole 环境配置文件示例
# 复制此文件为 .env 并修改相应的值

# =============================================================================
# 数据库配置
# =============================================================================
# SQLite (开发环境推荐)
DATABASE_URL=sqlite:///./data/loop_hole.db

# PostgreSQL (生产环境推荐)
# DATABASE_URL=postgresql://username:password@localhost:5432/loop_hole

# =============================================================================
# Redis 配置
# =============================================================================
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# 应用配置
# =============================================================================
SECRET_KEY=your-secret-key-change-in-production-please-use-a-strong-random-key
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

DEBUG=true
LOG_LEVEL=info
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# =============================================================================
# 任务调度配置
# =============================================================================
# 是否使用内存调度器 (开发环境可设为true，生产环境建议false)
USE_MEMORY_SCHEDULER=false

# Celery 配置 (如果不设置，将使用 REDIS_URL)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_WORKER_CONCURRENCY=4

# =============================================================================
# 浏览器自动化配置
# =============================================================================
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=3600
ANALYSIS_CACHE_TTL=86400

# =============================================================================
# 限流配置
# =============================================================================
RATE_LIMIT_PER_MINUTE=60

# =============================================================================
# 外部服务配置 (可选)
# =============================================================================
# MinIO 对象存储 (用于存储截图等文件)
# MINIO_ENDPOINT=localhost:9000
# MINIO_ACCESS_KEY=minioadmin
# MINIO_SECRET_KEY=minioadmin
# MINIO_BUCKET=loop-hole

# =============================================================================
# 监控配置
# =============================================================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# =============================================================================
# 前端配置
# =============================================================================
VITE_API_BASE_URL=http://localhost:8000
