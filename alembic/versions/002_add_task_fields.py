"""Add missing fields to extraction_tasks

Revision ID: 002_add_task_fields
Revises: 001_initial_schema
Create Date: 2025-08-15 22:27:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '002_add_task_fields'
down_revision = '001_initial_schema'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add missing fields to extraction_tasks table
    op.add_column('extraction_tasks', sa.Column('last_run', sa.DateTime(timezone=True), nullable=True))
    op.add_column('extraction_tasks', sa.Column('last_success', sa.DateTime(timezone=True), nullable=True))
    op.add_column('extraction_tasks', sa.Column('total_runs', sa.Integer(), nullable=True, default=0))
    op.add_column('extraction_tasks', sa.Column('next_run', sa.DateTime(timezone=True), nullable=True))
    op.add_column('extraction_tasks', sa.Column('error_message', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove the added fields
    op.drop_column('extraction_tasks', 'error_message')
    op.drop_column('extraction_tasks', 'next_run')
    op.drop_column('extraction_tasks', 'total_runs')
    op.drop_column('extraction_tasks', 'last_success')
    op.drop_column('extraction_tasks', 'last_run')
