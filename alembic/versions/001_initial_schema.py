"""Initial database schema

Revision ID: 001_initial_schema
Revises: 
Create Date: 2024-08-15 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_initial_schema'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create users table
    op.create_table('users',
    sa.<PERSON>umn('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=True),
    sa.Column('role', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    
    # Create user_sessions table
    op.create_table('user_sessions',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('token_hash', sa.String(length=64), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_sessions_expires_at'), 'user_sessions', ['expires_at'], unique=False)
    op.create_index(op.f('ix_user_sessions_token_hash'), 'user_sessions', ['token_hash'], unique=True)
    op.create_index(op.f('ix_user_sessions_user_id'), 'user_sessions', ['user_id'], unique=False)
    
    # Create extraction_tasks table
    op.create_table('extraction_tasks',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('url', sa.Text(), nullable=False),
    sa.Column('extraction_rules', sa.JSON(), nullable=False),
    sa.Column('schedule_config', sa.JSON(), nullable=True),
    sa.Column('auth_config', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extraction_tasks_name'), 'extraction_tasks', ['name'], unique=False)
    op.create_index(op.f('ix_extraction_tasks_status'), 'extraction_tasks', ['status'], unique=False)
    
    # Create extraction_results table
    op.create_table('extraction_results',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('task_id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('job_id', sa.String(length=255), nullable=True),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.Column('data_hash', sa.String(length=64), nullable=True),
    sa.Column('record_count', sa.Integer(), nullable=True),
    sa.Column('extraction_duration', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('extracted_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['extraction_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extraction_results_data_hash'), 'extraction_results', ['data_hash'], unique=False)
    op.create_index(op.f('ix_extraction_results_extracted_at'), 'extraction_results', ['extracted_at'], unique=False)
    op.create_index(op.f('ix_extraction_results_job_id'), 'extraction_results', ['job_id'], unique=False)
    op.create_index(op.f('ix_extraction_results_status'), 'extraction_results', ['status'], unique=False)
    op.create_index(op.f('ix_extraction_results_task_id'), 'extraction_results', ['task_id'], unique=False)
    
    # Create page_analysis_cache table
    op.create_table('page_analysis_cache',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('url_hash', sa.String(length=64), nullable=True),
    sa.Column('url', sa.Text(), nullable=False),
    sa.Column('analysis_result', sa.JSON(), nullable=False),
    sa.Column('confidence_score', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_page_analysis_cache_expires_at'), 'page_analysis_cache', ['expires_at'], unique=False)
    op.create_index(op.f('ix_page_analysis_cache_url_hash'), 'page_analysis_cache', ['url_hash'], unique=True)


def downgrade() -> None:
    op.drop_table('page_analysis_cache')
    op.drop_table('extraction_results')
    op.drop_table('extraction_tasks')
    op.drop_table('user_sessions')
    op.drop_table('users')
