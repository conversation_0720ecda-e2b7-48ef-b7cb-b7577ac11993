version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: loop_hole_postgres_dev
    environment:
      POSTGRES_DB: loop_hole
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: loop_hole_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 开发环境主应用服务
  app:
    build: .
    container_name: loop_hole_app_dev
    environment:
      - DATABASE_URL=********************************************/loop_hole
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key
      - JWT_SECRET_KEY=dev-jwt-secret-key
      - DEBUG=true
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    restart: unless-stopped

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  default:
    name: loop_hole_dev_network
