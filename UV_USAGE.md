# UV 依赖管理使用指南

本项目已从 pip 迁移到 uv 进行依赖管理。

## 环境要求

- Python 3.11+
- uv 包管理器
- conda 环境 (loop_hole)

## 安装依赖

```bash
# 安装基础依赖
uv sync

# 安装包含 PostgreSQL 支持的依赖
uv sync --extra postgres

# 安装开发依赖
uv sync --extra dev

# 安装所有依赖
uv sync --all-extras
```

## 运行项目

```bash
# 使用 uv 运行 Python 脚本
uv run python app/main.py

# 启动 FastAPI 服务器
uv run uvicorn app.main:app --reload

# 运行测试
uv run pytest

# 运行代码格式化
uv run black .
uv run isort .
```

## 添加新依赖

```bash
# 添加生产依赖
uv add package_name

# 添加开发依赖
uv add --dev package_name

# 添加可选依赖
uv add --optional extra_name package_name
```

## 项目结构

- `pyproject.toml` - 项目配置和依赖定义
- `uv.lock` - 锁定的依赖版本（自动生成）
- `.venv/` - 虚拟环境目录
- `.uvignore` - uv 忽略文件

## 迁移说明

项目已从 `requirements.txt` 迁移到 `pyproject.toml`：

- ✅ 所有依赖已迁移到 `pyproject.toml`
- ✅ 配置了开发依赖 (pytest, black, isort, pylint)
- ✅ 配置了可选依赖 (PostgreSQL 支持)
- ✅ 设置了代码质量工具配置
- ✅ 验证了依赖安装和导入

## 优势

- 🚀 更快的依赖解析和安装
- 🔒 更可靠的依赖锁定
- 📦 统一的项目配置文件
- 🛠️ 更好的开发工具集成