.PHONY: help build up down logs test clean install dev prod

# 默认目标
help:
	@echo "Loop Hole - 智能网页数据提取系统"
	@echo ""
	@echo "可用命令:"
	@echo "  install     - 安装依赖"
	@echo "  dev         - 启动开发环境"
	@echo "  prod        - 启动生产环境"
	@echo "  build       - 构建Docker镜像"
	@echo "  up          - 启动所有服务"
	@echo "  down        - 停止所有服务"
	@echo "  logs        - 查看日志"
	@echo "  test        - 运行测试"
	@echo "  clean       - 清理资源"
	@echo "  migrate     - 运行数据库迁移"
	@echo "  shell       - 进入应用容器shell"

# 安装依赖
install:
	pip install -r requirements.txt
	cd frontend && npm install

# 开发环境
dev:
	docker-compose -f docker-compose.dev.yml up -d
	@echo "开发环境已启动"
	@echo "API: http://localhost:8000"
	@echo "API文档: http://localhost:8000/docs"

# 生产环境
prod:
	docker-compose up -d
	@echo "生产环境已启动"
	@echo "应用: http://localhost"

# 构建镜像
build:
	docker-compose build

# 启动服务
up:
	docker-compose up -d

# 停止服务
down:
	docker-compose down

# 查看日志
logs:
	docker-compose logs -f

# 运行测试
test:
	python -m pytest tests/ -v
	cd frontend && npm run test

# 清理资源
clean:
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

# 数据库迁移
migrate:
	docker-compose exec app alembic upgrade head

# 进入应用容器
shell:
	docker-compose exec app bash

# 代码格式化
format:
	black app/ tests/
	isort app/ tests/

# 代码检查
lint:
	pylint app/
	black --check app/ tests/
	isort --check-only app/ tests/

# 前端开发
frontend-dev:
	cd frontend && npm run dev

# 前端构建
frontend-build:
	cd frontend && npm run build

# 备份数据库
backup:
	docker-compose exec postgres pg_dump -U postgres loop_hole > backup_$(shell date +%Y%m%d_%H%M%S).sql

# 恢复数据库
restore:
	@read -p "输入备份文件名: " file; \
	docker-compose exec -T postgres psql -U postgres loop_hole < $$file
