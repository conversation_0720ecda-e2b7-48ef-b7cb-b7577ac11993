import asyncio
from typing import Dict, List, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, Browser
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime

from app.utils.logger import get_logger
from app.core.analyzer import PageAnalyzer, AnalysisResult

logger = get_logger(__name__)

class DataExtractor:
    """数据提取器 - 使用Playwright进行浏览器自动化"""
    
    def __init__(self, headless: bool = True, timeout: int = 30000):
        self.headless = headless
        self.timeout = timeout
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.analyzer = PageAnalyzer()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            self.page = await self.browser.new_page()
            
            # 设置默认超时
            self.page.set_default_timeout(self.timeout)
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            logger.info("Browser started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
    
    async def extract_data(
        self,
        url: str,
        extraction_rules: Dict[str, Any],
        auth_config: Optional[Dict[str, Any]] = None,
        wait_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        提取数据
        
        Args:
            url: 目标URL
            extraction_rules: 提取规则
            auth_config: 认证配置
            wait_config: 等待配置
            
        Returns:
            提取的数据
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting data extraction from {url}")
            
            # 导航到页面
            await self._navigate_to_page(url, auth_config)
            
            # 等待页面加载
            await self._wait_for_page_load(wait_config)
            
            # 获取页面内容
            html_content = await self.page.content()
            
            # 执行提取规则
            extracted_data = await self._apply_extraction_rules(
                html_content, extraction_rules
            )
            
            # 计算执行时间
            duration = time.time() - start_time
            
            result = {
                "status": "success",
                "data": extracted_data,
                "metadata": {
                    "url": url,
                    "extraction_time": datetime.utcnow().isoformat(),
                    "duration": round(duration, 2),
                    "page_title": await self.page.title(),
                    "record_count": self._count_records(extracted_data)
                }
            }
            
            logger.info(f"Data extraction completed in {duration:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "metadata": {
                    "url": url,
                    "extraction_time": datetime.utcnow().isoformat(),
                    "duration": time.time() - start_time
                }
            }
    
    async def _navigate_to_page(
        self, 
        url: str, 
        auth_config: Optional[Dict[str, Any]] = None
    ):
        """导航到目标页面"""
        # 处理认证
        if auth_config:
            await self._handle_authentication(auth_config)
        
        # 导航到目标页面
        response = await self.page.goto(url, wait_until='networkidle')
        
        if not response or response.status >= 400:
            raise Exception(f"Failed to load page: HTTP {response.status if response else 'Unknown'}")
    
    async def _handle_authentication(self, auth_config: Dict[str, Any]):
        """处理页面认证"""
        auth_type = auth_config.get("type", "form")
        
        if auth_type == "form":
            # 表单登录
            login_url = auth_config.get("login_url")
            username = auth_config.get("username")
            password = auth_config.get("password")
            
            if login_url:
                await self.page.goto(login_url)
                
                # 填写用户名
                username_selector = auth_config.get("username_selector", "input[name='username']")
                await self.page.fill(username_selector, username)
                
                # 填写密码
                password_selector = auth_config.get("password_selector", "input[name='password']")
                await self.page.fill(password_selector, password)
                
                # 点击登录按钮
                submit_selector = auth_config.get("submit_selector", "button[type='submit']")
                await self.page.click(submit_selector)
                
                # 等待登录完成
                await self.page.wait_for_load_state('networkidle')
        
        elif auth_type == "cookie":
            # Cookie认证
            cookies = auth_config.get("cookies", [])
            await self.page.context.add_cookies(cookies)
        
        elif auth_type == "header":
            # Header认证
            headers = auth_config.get("headers", {})
            await self.page.set_extra_http_headers(headers)
    
    async def _wait_for_page_load(self, wait_config: Optional[Dict[str, Any]] = None):
        """等待页面加载完成"""
        if not wait_config:
            await self.page.wait_for_load_state('networkidle')
            return
        
        wait_type = wait_config.get("type", "networkidle")
        
        if wait_type == "selector":
            # 等待特定元素出现
            selector = wait_config.get("selector")
            timeout = wait_config.get("timeout", self.timeout)
            await self.page.wait_for_selector(selector, timeout=timeout)
        
        elif wait_type == "function":
            # 等待JavaScript函数返回true
            function = wait_config.get("function")
            timeout = wait_config.get("timeout", self.timeout)
            await self.page.wait_for_function(function, timeout=timeout)
        
        elif wait_type == "delay":
            # 固定延迟
            delay = wait_config.get("delay", 1000)
            await asyncio.sleep(delay / 1000)
        
        else:
            # 默认等待网络空闲
            await self.page.wait_for_load_state('networkidle')
    
    async def _apply_extraction_rules(
        self, 
        html_content: str, 
        extraction_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用提取规则"""
        extracted_data = {}
        
        for rule_name, rule_config in extraction_rules.items():
            try:
                rule_type = rule_config.get("type", "css")
                
                if rule_type == "css":
                    # CSS选择器提取
                    data = await self._extract_by_css(rule_config)
                
                elif rule_type == "xpath":
                    # XPath提取
                    data = await self._extract_by_xpath(rule_config)
                
                elif rule_type == "table":
                    # 表格提取
                    data = await self._extract_table_data(rule_config)
                
                elif rule_type == "auto":
                    # 自动分析提取
                    data = await self._extract_by_analysis(html_content, rule_config)
                
                else:
                    logger.warning(f"Unknown extraction rule type: {rule_type}")
                    continue
                
                extracted_data[rule_name] = data
                
            except Exception as e:
                logger.error(f"Failed to apply rule '{rule_name}': {e}")
                extracted_data[rule_name] = {"error": str(e)}
        
        return extracted_data
    
    async def _extract_by_css(self, rule_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用CSS选择器提取数据"""
        selector = rule_config.get("selector")
        fields = rule_config.get("fields", {})
        
        elements = await self.page.query_selector_all(selector)
        results = []
        
        for element in elements:
            item = {}
            
            for field_name, field_selector in fields.items():
                try:
                    if field_selector.startswith("@"):
                        # 属性值
                        attr_name = field_selector[1:]
                        value = await element.get_attribute(attr_name)
                    else:
                        # 文本内容
                        field_element = await element.query_selector(field_selector)
                        value = await field_element.text_content() if field_element else None
                    
                    item[field_name] = value
                    
                except Exception as e:
                    logger.warning(f"Failed to extract field '{field_name}': {e}")
                    item[field_name] = None
            
            results.append(item)
        
        return results
    
    async def _extract_by_xpath(self, rule_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用XPath提取数据"""
        # Playwright不直接支持XPath，需要转换为CSS选择器或使用evaluate
        xpath = rule_config.get("xpath")
        
        # 使用JavaScript执行XPath查询
        result = await self.page.evaluate(f"""
            () => {{
                const elements = document.evaluate(
                    '{xpath}',
                    document,
                    null,
                    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                    null
                );
                
                const results = [];
                for (let i = 0; i < elements.snapshotLength; i++) {{
                    const element = elements.snapshotItem(i);
                    results.push(element.textContent.trim());
                }}
                
                return results;
            }}
        """)
        
        return [{"text": text} for text in result]
    
    async def _extract_table_data(self, rule_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取表格数据"""
        table_selector = rule_config.get("selector", "table")
        
        # 获取表格元素
        table = await self.page.query_selector(table_selector)
        if not table:
            return []
        
        # 提取表头
        headers = []
        header_rows = await table.query_selector_all("thead tr, tr:first-child")
        if header_rows:
            header_cells = await header_rows[0].query_selector_all("th, td")
            for cell in header_cells:
                text = await cell.text_content()
                headers.append(text.strip())
        
        # 提取数据行
        data_rows = await table.query_selector_all("tbody tr, tr:not(:first-child)")
        results = []
        
        for row in data_rows:
            cells = await row.query_selector_all("td, th")
            row_data = {}
            
            for i, cell in enumerate(cells):
                text = await cell.text_content()
                header = headers[i] if i < len(headers) else f"column_{i}"
                row_data[header] = text.strip()
            
            if row_data:  # 只添加非空行
                results.append(row_data)
        
        return results
    
    async def _extract_by_analysis(
        self, 
        html_content: str, 
        rule_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """使用智能分析提取数据"""
        # 使用PageAnalyzer分析页面结构
        analysis_result = self.analyzer.analyze_page(html_content, "")
        
        extracted_data = {}
        
        # 提取表格数据
        if analysis_result.tables:
            table_data = []
            for table in analysis_result.tables:
                if table.confidence > 0.7:  # 只提取高置信度的表格
                    data = await self._extract_table_data({
                        "selector": table.selector
                    })
                    table_data.extend(data)
            
            if table_data:
                extracted_data["tables"] = table_data
        
        # 提取卡片数据
        if analysis_result.cards:
            card_data = []
            for card in analysis_result.cards:
                if card.confidence > 0.7:
                    data = await self._extract_by_css({
                        "selector": card.selector,
                        "fields": {field: field for field in card.fields}
                    })
                    card_data.extend(data)
            
            if card_data:
                extracted_data["cards"] = card_data
        
        return extracted_data
    
    def _count_records(self, data: Dict[str, Any]) -> int:
        """统计提取的记录数"""
        total = 0
        
        for value in data.values():
            if isinstance(value, list):
                total += len(value)
            elif isinstance(value, dict) and "error" not in value:
                total += 1
        
        return total

# 异步上下文管理器工厂函数
def create_extractor(**kwargs):
    """创建数据提取器的异步上下文管理器"""
    return DataExtractor(**kwargs)
