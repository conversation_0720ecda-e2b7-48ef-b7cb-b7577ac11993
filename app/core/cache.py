try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timezone, timedelta
import hashlib
import os
import threading
import time
from collections import defaultdict

from app.utils.logger import get_logger

logger = get_logger(__name__)

class MemoryCacheManager:
    """内存缓存管理器 - 与Redis接口兼容"""

    def __init__(self):
        self._cache = {}  # 存储缓存数据
        self._expiry = {}  # 存储过期时间
        self._lock = threading.RLock()  # 线程安全锁
        self._cleanup_interval = 60  # 清理间隔（秒）
        self._last_cleanup = time.time()

    def _cleanup_expired(self):
        """清理过期的缓存项"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return

        with self._lock:
            expired_keys = []
            for key, expiry_time in self._expiry.items():
                if current_time > expiry_time:
                    expired_keys.append(key)

            for key in expired_keys:
                self._cache.pop(key, None)
                self._expiry.pop(key, None)

            self._last_cleanup = current_time
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def _is_expired(self, key: str) -> bool:
        """检查键是否过期"""
        if key not in self._expiry:
            return False
        return time.time() > self._expiry[key]

    def setex(self, key: str, ttl: int, value: bytes) -> bool:
        """设置带过期时间的缓存（兼容Redis接口）"""
        try:
            with self._lock:
                self._cleanup_expired()
                self._cache[key] = value
                self._expiry[key] = time.time() + ttl
                return True
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False

    def get(self, key: str) -> Optional[bytes]:
        """获取缓存值（兼容Redis接口）"""
        try:
            with self._lock:
                self._cleanup_expired()
                if key not in self._cache or self._is_expired(key):
                    # 如果过期，立即清理
                    if key in self._cache:
                        self._cache.pop(key, None)
                        self._expiry.pop(key, None)
                    return None
                return self._cache[key]
        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None

    def delete(self, key: str) -> int:
        """删除缓存键（兼容Redis接口）"""
        try:
            with self._lock:
                if key in self._cache:
                    self._cache.pop(key, None)
                    self._expiry.pop(key, None)
                    return 1
                return 0
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return 0

    def exists(self, key: str) -> int:
        """检查键是否存在（兼容Redis接口）"""
        try:
            with self._lock:
                self._cleanup_expired()
                if key in self._cache and not self._is_expired(key):
                    return 1
                return 0
        except Exception as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return 0

    def expire(self, key: str, ttl: int) -> bool:
        """设置键的过期时间（兼容Redis接口）"""
        try:
            with self._lock:
                if key in self._cache and not self._is_expired(key):
                    self._expiry[key] = time.time() + ttl
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to set expiration for key {key}: {e}")
            return False

    def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的键列表（兼容Redis接口）"""
        try:
            with self._lock:
                self._cleanup_expired()
                if pattern == "*":
                    return [k for k in self._cache.keys() if not self._is_expired(k)]

                # 简单的模式匹配（支持*通配符）
                import fnmatch
                return [k for k in self._cache.keys()
                       if fnmatch.fnmatch(k, pattern) and not self._is_expired(k)]
        except Exception as e:
            logger.error(f"Failed to get keys with pattern {pattern}: {e}")
            return []

    def ping(self) -> bool:
        """健康检查（兼容Redis接口）"""
        return True

    def clear_all(self):
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            self._expiry.clear()
            logger.info("Cleared all cache entries")

class CacheManager:
    """缓存管理器 - 支持Redis和内存缓存"""

    def __init__(self, redis_url: str = None, use_memory_cache: bool = None):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.use_memory_cache = use_memory_cache if use_memory_cache is not None else os.getenv("USE_MEMORY_CACHE", "false").lower() == "true"
        self.redis_client = None
        self.memory_client = None
        self.default_ttl = 3600  # 默认1小时过期
        self.connect()

    def connect(self):
        """连接缓存后端"""
        if self.use_memory_cache or not REDIS_AVAILABLE:
            # 使用内存缓存（用户指定或redis不可用）
            try:
                self.memory_client = MemoryCacheManager()
                if not REDIS_AVAILABLE:
                    logger.info("Redis module not available, using memory cache")
                else:
                    logger.info("Using memory cache successfully")
                return
            except Exception as e:
                logger.error(f"Failed to initialize memory cache: {e}")

        # 尝试连接Redis（仅在redis可用时）
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(
                    self.redis_url,
                    decode_responses=False,  # 保持二进制数据
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
                # 测试连接
                self.redis_client.ping()
                logger.info("Connected to Redis successfully")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis: {e}. Falling back to memory cache.")
                self.redis_client = None
                # 回退到内存缓存
                try:
                    self.memory_client = MemoryCacheManager()
                    logger.info("Fallback to memory cache successfully")
                except Exception as mem_e:
                    logger.error(f"Failed to initialize fallback memory cache: {mem_e}")

    def _get_client(self):
        """获取当前可用的缓存客户端"""
        if self.memory_client:
            return self.memory_client
        return self.redis_client

    def _serialize_key(self, key: str) -> str:
        """序列化缓存键"""
        return f"loop_hole:{key}"

    def _serialize_value(self, value: Any) -> bytes:
        """序列化缓存值"""
        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value).encode('utf-8')
        else:
            return pickle.dumps(value)

    def _deserialize_value(self, value: bytes) -> Any:
        """反序列化缓存值"""
        try:
            # 先尝试JSON反序列化
            return json.loads(value.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 如果失败，使用pickle
            return pickle.loads(value)

    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存"""
        client = self._get_client()
        if not client:
            return False

        try:
            serialized_key = self._serialize_key(key)
            serialized_value = self._serialize_value(value)
            ttl = ttl or self.default_ttl

            result = client.setex(
                serialized_key,
                ttl,
                serialized_value
            )

            logger.debug(f"Set cache key: {key}, TTL: {ttl}")
            return result

        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False

    def get(self, key: str) -> Any:
        """获取缓存"""
        client = self._get_client()
        if not client:
            return None

        try:
            serialized_key = self._serialize_key(key)
            value = client.get(serialized_key)

            if value is None:
                logger.debug(f"Cache miss for key: {key}")
                return None

            logger.debug(f"Cache hit for key: {key}")
            return self._deserialize_value(value)

        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None

    def delete(self, key: str) -> bool:
        """删除缓存"""
        client = self._get_client()
        if not client:
            return False

        try:
            serialized_key = self._serialize_key(key)
            result = client.delete(serialized_key)

            logger.debug(f"Deleted cache key: {key}")
            return bool(result)

        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False

    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        client = self._get_client()
        if not client:
            return False

        try:
            serialized_key = self._serialize_key(key)
            return bool(client.exists(serialized_key))
        except Exception as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return False

    def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        client = self._get_client()
        if not client:
            return False

        try:
            serialized_key = self._serialize_key(key)
            result = client.expire(serialized_key, ttl)

            logger.debug(f"Set expiration for key {key}: {ttl}s")
            return bool(result)

        except Exception as e:
            logger.error(f"Failed to set expiration for key {key}: {e}")
            return False

    def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存"""
        client = self._get_client()
        if not client:
            return 0

        try:
            pattern = self._serialize_key(pattern)
            keys = client.keys(pattern)

            if keys:
                if hasattr(client, 'delete') and len(keys) > 1:
                    # Redis支持批量删除
                    if self.redis_client:
                        deleted = client.delete(*keys)
                    else:
                        # 内存缓存需要逐个删除
                        deleted = 0
                        for key in keys:
                            deleted += client.delete(key)
                else:
                    deleted = client.delete(keys[0]) if keys else 0

                logger.info(f"Cleared {deleted} cache keys matching pattern: {pattern}")
                return deleted

            return 0

        except Exception as e:
            logger.error(f"Failed to clear cache pattern {pattern}: {e}")
            return 0

class ExtractionCache:
    """数据提取缓存"""

    def __init__(self, cache_manager: CacheManager = None):
        self.cache = cache_manager or CacheManager()
        self.extraction_ttl = 7200  # 提取结果缓存2小时
        self.analysis_ttl = 86400   # 页面分析缓存24小时

    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """生成缓存键"""
        # 创建一个基于参数的哈希键
        key_data = json.dumps(kwargs, sort_keys=True)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"{prefix}:{key_hash}"

    def cache_extraction_result(
        self,
        url: str,
        extraction_rules: Dict[str, Any],
        result: Dict[str, Any],
        ttl: int = None
    ) -> bool:
        """缓存提取结果"""
        cache_key = self._generate_cache_key(
            "extraction",
            url=url,
            rules=extraction_rules
        )

        cache_data = {
            "result": result,
            "cached_at": datetime.now(timezone.utc).isoformat(),
            "url": url,
            "rules": extraction_rules
        }

        return self.cache.set(
            cache_key,
            cache_data,
            ttl or self.extraction_ttl
        )

    def get_cached_extraction(
        self,
        url: str,
        extraction_rules: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的提取结果"""
        cache_key = self._generate_cache_key(
            "extraction",
            url=url,
            rules=extraction_rules
        )

        cached_data = self.cache.get(cache_key)

        if cached_data:
            logger.info(f"Using cached extraction result for {url}")
            return cached_data.get("result")

        return None

    def cache_page_analysis(
        self,
        url: str,
        html_hash: str,
        analysis_result: Dict[str, Any],
        ttl: int = None
    ) -> bool:
        """缓存页面分析结果"""
        cache_key = self._generate_cache_key(
            "analysis",
            url=url,
            html_hash=html_hash
        )

        cache_data = {
            "analysis": analysis_result,
            "cached_at": datetime.now(timezone.utc).isoformat(),
            "url": url,
            "html_hash": html_hash
        }

        return self.cache.set(
            cache_key,
            cache_data,
            ttl or self.analysis_ttl
        )

    def get_cached_analysis(
        self,
        url: str,
        html_hash: str
    ) -> Optional[Dict[str, Any]]:
        """获取缓存的页面分析结果"""
        cache_key = self._generate_cache_key(
            "analysis",
            url=url,
            html_hash=html_hash
        )

        cached_data = self.cache.get(cache_key)

        if cached_data:
            logger.info(f"Using cached analysis result for {url}")
            return cached_data.get("analysis")

        return None

    def invalidate_url_cache(self, url: str) -> int:
        """清除特定URL的所有缓存"""
        # 清除提取结果缓存
        extraction_pattern = f"extraction:*{hashlib.md5(url.encode()).hexdigest()[:8]}*"
        extraction_cleared = self.cache.clear_pattern(extraction_pattern)

        # 清除分析结果缓存
        analysis_pattern = f"analysis:*{hashlib.md5(url.encode()).hexdigest()[:8]}*"
        analysis_cleared = self.cache.clear_pattern(analysis_pattern)

        total_cleared = extraction_cleared + analysis_cleared
        logger.info(f"Invalidated {total_cleared} cache entries for URL: {url}")

        return total_cleared

class SessionCache:
    """会话缓存"""

    def __init__(self, cache_manager: CacheManager = None):
        self.cache = cache_manager or CacheManager()
        self.session_ttl = 86400  # 会话缓存24小时

    def set_user_session(
        self,
        user_id: str,
        session_data: Dict[str, Any],
        ttl: int = None
    ) -> bool:
        """设置用户会话"""
        cache_key = f"session:user:{user_id}"

        session_info = {
            "user_id": user_id,
            "data": session_data,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "last_accessed": datetime.now(timezone.utc).isoformat()
        }

        return self.cache.set(
            cache_key,
            session_info,
            ttl or self.session_ttl
        )

    def get_user_session(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户会话"""
        cache_key = f"session:user:{user_id}"
        session_info = self.cache.get(cache_key)

        if session_info:
            # 更新最后访问时间
            session_info["last_accessed"] = datetime.now(timezone.utc).isoformat()
            self.cache.set(cache_key, session_info, self.session_ttl)

            return session_info.get("data")

        return None

    def delete_user_session(self, user_id: str) -> bool:
        """删除用户会话"""
        cache_key = f"session:user:{user_id}"
        return self.cache.delete(cache_key)

class MetricsCache:
    """指标缓存"""

    def __init__(self, cache_manager: CacheManager = None):
        self.cache = cache_manager or CacheManager()
        self.metrics_ttl = 300  # 指标缓存5分钟

    def cache_system_metrics(self, metrics: Dict[str, Any]) -> bool:
        """缓存系统指标"""
        cache_key = "metrics:system"

        metrics_data = {
            "metrics": metrics,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        return self.cache.set(cache_key, metrics_data, self.metrics_ttl)

    def get_system_metrics(self) -> Optional[Dict[str, Any]]:
        """获取系统指标"""
        cache_key = "metrics:system"
        cached_data = self.cache.get(cache_key)

        if cached_data:
            return cached_data.get("metrics")

        return None

    def cache_task_stats(self, stats: Dict[str, Any]) -> bool:
        """缓存任务统计"""
        cache_key = "metrics:tasks"

        stats_data = {
            "stats": stats,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        return self.cache.set(cache_key, stats_data, self.metrics_ttl)

    def get_task_stats(self) -> Optional[Dict[str, Any]]:
        """获取任务统计"""
        cache_key = "metrics:tasks"
        cached_data = self.cache.get(cache_key)

        if cached_data:
            return cached_data.get("stats")

        return None

# 全局缓存实例
# 默认使用内存缓存，可通过环境变量 USE_MEMORY_CACHE=false 切换到Redis
cache_manager = CacheManager(use_memory_cache=True)
extraction_cache = ExtractionCache(cache_manager)
session_cache = SessionCache(cache_manager)
metrics_cache = MetricsCache(cache_manager)
