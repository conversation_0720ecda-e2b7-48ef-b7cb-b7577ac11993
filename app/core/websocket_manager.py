from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio
from datetime import datetime
import logging
from uuid import UUID

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户订阅的任务
        self.user_subscriptions: Dict[str, Set[str]] = {}
        # 任务订阅者
        self.task_subscribers: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_subscriptions[user_id] = set()
        
        logger.info(f"User {user_id} connected via WebSocket")
        
        # 发送连接确认消息
        await self.send_personal_message({
            "type": "connection_established",
            "message": "WebSocket连接已建立",
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id
        }, user_id)
    
    def disconnect(self, user_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
            
        # 清理订阅关系
        if user_id in self.user_subscriptions:
            subscribed_tasks = self.user_subscriptions[user_id]
            for task_id in subscribed_tasks:
                if task_id in self.task_subscribers:
                    self.task_subscribers[task_id].discard(user_id)
                    if not self.task_subscribers[task_id]:
                        del self.task_subscribers[task_id]
            del self.user_subscriptions[user_id]
            
        logger.info(f"User {user_id} disconnected from WebSocket")
    
    async def send_personal_message(self, message: dict, user_id: str):
        """发送个人消息"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"Failed to send message to user {user_id}: {e}")
                # 连接可能已断开，清理连接
                self.disconnect(user_id)
    
    async def broadcast_to_task_subscribers(self, message: dict, task_id: str):
        """向任务订阅者广播消息"""
        if task_id in self.task_subscribers:
            subscribers = self.task_subscribers[task_id].copy()
            for user_id in subscribers:
                await self.send_personal_message(message, user_id)
    
    async def broadcast_to_all(self, message: dict):
        """向所有连接的用户广播消息"""
        disconnected_users = []
        
        for user_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"Failed to broadcast to user {user_id}: {e}")
                disconnected_users.append(user_id)
        
        # 清理断开的连接
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    def subscribe_to_task(self, user_id: str, task_id: str):
        """订阅任务状态更新"""
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = set()
        
        self.user_subscriptions[user_id].add(task_id)
        
        if task_id not in self.task_subscribers:
            self.task_subscribers[task_id] = set()
        
        self.task_subscribers[task_id].add(user_id)
        
        logger.info(f"User {user_id} subscribed to task {task_id}")
    
    def unsubscribe_from_task(self, user_id: str, task_id: str):
        """取消订阅任务状态更新"""
        if user_id in self.user_subscriptions:
            self.user_subscriptions[user_id].discard(task_id)
        
        if task_id in self.task_subscribers:
            self.task_subscribers[task_id].discard(user_id)
            if not self.task_subscribers[task_id]:
                del self.task_subscribers[task_id]
        
        logger.info(f"User {user_id} unsubscribed from task {task_id}")
    
    def get_connection_stats(self) -> dict:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.user_subscriptions.values()),
            "active_tasks": len(self.task_subscribers),
            "connected_users": list(self.active_connections.keys())
        }

# 全局连接管理器实例
manager = ConnectionManager()

class WebSocketNotifier:
    """WebSocket通知器"""
    
    @staticmethod
    async def notify_task_status_change(task_id: str, status: str, details: dict = None):
        """通知任务状态变化"""
        message = {
            "type": "task_status_update",
            "task_id": task_id,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        
        await manager.broadcast_to_task_subscribers(message, task_id)
        logger.info(f"Notified task {task_id} status change: {status}")
    
    @staticmethod
    async def notify_job_progress(job_id: str, task_id: str, progress: float, message: str = None):
        """通知作业进度更新"""
        notification = {
            "type": "job_progress_update",
            "job_id": job_id,
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        await manager.broadcast_to_task_subscribers(notification, task_id)
        logger.info(f"Notified job {job_id} progress: {progress:.1%}")
    
    @staticmethod
    async def notify_extraction_result(task_id: str, result_id: str, record_count: int, success: bool):
        """通知数据提取结果"""
        message = {
            "type": "extraction_completed",
            "task_id": task_id,
            "result_id": result_id,
            "record_count": record_count,
            "success": success,
            "timestamp": datetime.now().isoformat()
        }
        
        await manager.broadcast_to_task_subscribers(message, task_id)
        logger.info(f"Notified extraction result for task {task_id}: {record_count} records")
    
    @staticmethod
    async def notify_system_alert(alert_type: str, message: str, severity: str = "info"):
        """发送系统告警"""
        notification = {
            "type": "system_alert",
            "alert_type": alert_type,
            "message": message,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        }
        
        await manager.broadcast_to_all(notification)
        logger.warning(f"System alert [{severity}]: {message}")

# 导出通知器实例
notifier = WebSocketNotifier()