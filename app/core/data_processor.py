"""
高级数据处理模块
提供数据清洗、标准化、去重、质量评分等功能
"""

import re
import hashlib
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import logging
from difflib import SequenceMatcher

from app.utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class DataQualityScore:
    """数据质量评分"""
    overall_score: float  # 总体评分 0-100
    completeness: float   # 完整性评分
    accuracy: float       # 准确性评分
    consistency: float    # 一致性评分
    uniqueness: float     # 唯一性评分
    validity: float       # 有效性评分
    details: Dict[str, Any]  # 详细信息

@dataclass
class ProcessingResult:
    """处理结果"""
    original_count: int
    processed_count: int
    removed_count: int
    cleaned_data: List[Dict[str, Any]]
    quality_score: DataQualityScore
    processing_log: List[str]

class DataProcessor:
    """高级数据处理器"""
    
    def __init__(self):
        self.cleaning_rules = {
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'phone': r'^[\+]?[1-9][\d]{0,15}$',
            'url': r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
            'number': r'^-?\d+(\.\d+)?$',
            'date': r'^\d{4}-\d{2}-\d{2}$|^\d{2}\/\d{2}\/\d{4}$'
        }
        
        self.similarity_threshold = 0.85  # 相似度阈值
        self.min_quality_score = 60      # 最低质量分数
    
    def process_data(
        self, 
        data: List[Dict[str, Any]], 
        config: Optional[Dict[str, Any]] = None
    ) -> ProcessingResult:
        """处理数据"""
        if not data:
            return ProcessingResult(
                original_count=0,
                processed_count=0,
                removed_count=0,
                cleaned_data=[],
                quality_score=DataQualityScore(0, 0, 0, 0, 0, 0, {}),
                processing_log=["输入数据为空"]
            )
        
        config = config or {}
        processing_log = []
        original_count = len(data)
        
        logger.info(f"开始处理数据，原始记录数: {original_count}")
        processing_log.append(f"开始处理数据，原始记录数: {original_count}")
        
        # 1. 数据清洗
        cleaned_data = self._clean_data(data, config.get('cleaning', {}))
        processing_log.append(f"数据清洗完成，清洗后记录数: {len(cleaned_data)}")
        
        # 2. 数据标准化
        if config.get('standardize', True):
            cleaned_data = self._standardize_data(cleaned_data)
            processing_log.append("数据标准化完成")
        
        # 3. 重复数据检测和去除
        if config.get('remove_duplicates', True):
            before_dedup = len(cleaned_data)
            cleaned_data = self._remove_duplicates(cleaned_data, config.get('dedup_config', {}))
            removed_duplicates = before_dedup - len(cleaned_data)
            processing_log.append(f"去重完成，移除重复记录: {removed_duplicates}")
        
        # 4. 数据验证
        if config.get('validate', True):
            cleaned_data = self._validate_data(cleaned_data, config.get('validation_rules', {}))
            processing_log.append("数据验证完成")
        
        # 5. 计算数据质量评分
        quality_score = self._calculate_quality_score(data, cleaned_data)
        processing_log.append(f"数据质量评分: {quality_score.overall_score:.1f}")
        
        processed_count = len(cleaned_data)
        removed_count = original_count - processed_count
        
        logger.info(f"数据处理完成，处理后记录数: {processed_count}，移除记录数: {removed_count}")
        
        return ProcessingResult(
            original_count=original_count,
            processed_count=processed_count,
            removed_count=removed_count,
            cleaned_data=cleaned_data,
            quality_score=quality_score,
            processing_log=processing_log
        )
    
    def _clean_data(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """数据清洗"""
        cleaned_data = []
        
        for record in data:
            cleaned_record = {}
            
            for key, value in record.items():
                if value is None:
                    if config.get('keep_null', False):
                        cleaned_record[key] = None
                    continue
                
                # 转换为字符串进行处理
                str_value = str(value).strip()
                
                # 空值处理
                if not str_value:
                    if config.get('keep_empty', False):
                        cleaned_record[key] = ""
                    continue
                
                # 数据类型清洗
                cleaned_value = self._clean_field_value(str_value, key, config)
                if cleaned_value is not None:
                    cleaned_record[key] = cleaned_value
            
            # 只保留有数据的记录
            if cleaned_record:
                cleaned_data.append(cleaned_record)
        
        return cleaned_data
    
    def _clean_field_value(self, value: str, field_name: str, config: Dict[str, Any]) -> Any:
        """清洗字段值"""
        # 移除多余空白字符
        value = re.sub(r'\s+', ' ', value).strip()
        
        # 根据字段名推断数据类型并清洗
        field_lower = field_name.lower()
        
        # 邮箱清洗
        if 'email' in field_lower or 'mail' in field_lower:
            if re.match(self.cleaning_rules['email'], value):
                return value.lower()
            return None
        
        # 电话号码清洗
        if 'phone' in field_lower or 'tel' in field_lower or 'mobile' in field_lower:
            # 移除非数字字符
            phone = re.sub(r'[^\d+]', '', value)
            if re.match(self.cleaning_rules['phone'], phone):
                return phone
            return None
        
        # URL清洗
        if 'url' in field_lower or 'link' in field_lower or 'website' in field_lower:
            if re.match(self.cleaning_rules['url'], value):
                return value.lower()
            return None
        
        # 数字清洗
        if 'price' in field_lower or 'amount' in field_lower or 'count' in field_lower:
            # 移除货币符号和逗号
            number_str = re.sub(r'[^\d.-]', '', value)
            if re.match(self.cleaning_rules['number'], number_str):
                try:
                    return float(number_str) if '.' in number_str else int(number_str)
                except ValueError:
                    pass
            return None
        
        # 日期清洗
        if 'date' in field_lower or 'time' in field_lower:
            if re.match(self.cleaning_rules['date'], value):
                return value
            return None
        
        return value
    
    def _standardize_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据标准化"""
        if not data:
            return data
        
        # 获取所有字段名
        all_fields = set()
        for record in data:
            all_fields.update(record.keys())
        
        # 标准化字段名
        field_mapping = {}
        for field in all_fields:
            standardized = self._standardize_field_name(field)
            field_mapping[field] = standardized
        
        # 应用标准化
        standardized_data = []
        for record in data:
            standardized_record = {}
            for old_field, value in record.items():
                new_field = field_mapping[old_field]
                standardized_record[new_field] = value
            standardized_data.append(standardized_record)
        
        return standardized_data
    
    def _standardize_field_name(self, field_name: str) -> str:
        """标准化字段名"""
        # 转换为小写
        name = field_name.lower()
        
        # 移除特殊字符，用下划线替换
        name = re.sub(r'[^\w\s]', '', name)
        name = re.sub(r'\s+', '_', name)
        
        # 移除多余的下划线
        name = re.sub(r'_+', '_', name).strip('_')
        
        return name
    
    def _remove_duplicates(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """去除重复数据"""
        if not data:
            return data
        
        unique_data = []
        seen_hashes = set()
        seen_similar = []
        
        for record in data:
            # 计算记录哈希
            record_hash = self._calculate_record_hash(record)
            
            # 精确重复检测
            if record_hash in seen_hashes:
                continue
            
            # 相似重复检测
            if config.get('fuzzy_dedup', False):
                is_similar = False
                for existing_record in seen_similar:
                    similarity = self._calculate_similarity(record, existing_record)
                    if similarity > self.similarity_threshold:
                        is_similar = True
                        break
                
                if is_similar:
                    continue
                
                seen_similar.append(record)
            
            seen_hashes.add(record_hash)
            unique_data.append(record)
        
        return unique_data
    
    def _calculate_record_hash(self, record: Dict[str, Any]) -> str:
        """计算记录哈希值"""
        # 排序字段以确保一致性
        sorted_items = sorted(record.items())
        record_str = str(sorted_items)
        return hashlib.md5(record_str.encode()).hexdigest()
    
    def _calculate_similarity(self, record1: Dict[str, Any], record2: Dict[str, Any]) -> float:
        """计算两条记录的相似度"""
        if not record1 or not record2:
            return 0.0
        
        # 获取共同字段
        common_fields = set(record1.keys()) & set(record2.keys())
        if not common_fields:
            return 0.0
        
        similarities = []
        for field in common_fields:
            val1 = str(record1[field])
            val2 = str(record2[field])
            similarity = SequenceMatcher(None, val1, val2).ratio()
            similarities.append(similarity)
        
        return sum(similarities) / len(similarities)
    
    def _validate_data(self, data: List[Dict[str, Any]], rules: Dict[str, Any]) -> List[Dict[str, Any]]:
        """数据验证"""
        if not rules:
            return data
        
        valid_data = []
        
        for record in data:
            is_valid = True
            
            # 必填字段检查
            required_fields = rules.get('required_fields', [])
            for field in required_fields:
                if field not in record or not record[field]:
                    is_valid = False
                    break
            
            # 字段值验证
            field_rules = rules.get('field_rules', {})
            for field, rule in field_rules.items():
                if field in record:
                    value = record[field]
                    if not self._validate_field_value(value, rule):
                        is_valid = False
                        break
            
            if is_valid:
                valid_data.append(record)
        
        return valid_data
    
    def _validate_field_value(self, value: Any, rule: Dict[str, Any]) -> bool:
        """验证字段值"""
        if value is None:
            return rule.get('allow_null', True)
        
        str_value = str(value)
        
        # 长度检查
        if 'min_length' in rule and len(str_value) < rule['min_length']:
            return False
        if 'max_length' in rule and len(str_value) > rule['max_length']:
            return False
        
        # 正则表达式检查
        if 'pattern' in rule and not re.match(rule['pattern'], str_value):
            return False
        
        # 数值范围检查
        if 'min_value' in rule or 'max_value' in rule:
            try:
                num_value = float(str_value)
                if 'min_value' in rule and num_value < rule['min_value']:
                    return False
                if 'max_value' in rule and num_value > rule['max_value']:
                    return False
            except ValueError:
                return False
        
        return True
    
    def _calculate_quality_score(
        self, 
        original_data: List[Dict[str, Any]], 
        processed_data: List[Dict[str, Any]]
    ) -> DataQualityScore:
        """计算数据质量评分"""
        if not original_data:
            return DataQualityScore(0, 0, 0, 0, 0, 0, {})
        
        # 完整性评分 (数据保留率)
        completeness = (len(processed_data) / len(original_data)) * 100
        
        # 准确性评分 (基于数据清洗效果)
        accuracy = self._calculate_accuracy_score(processed_data)
        
        # 一致性评分 (字段标准化程度)
        consistency = self._calculate_consistency_score(processed_data)
        
        # 唯一性评分 (重复数据比例)
        uniqueness = self._calculate_uniqueness_score(processed_data)
        
        # 有效性评分 (数据格式正确性)
        validity = self._calculate_validity_score(processed_data)
        
        # 总体评分 (加权平均)
        overall_score = (
            completeness * 0.2 +
            accuracy * 0.25 +
            consistency * 0.2 +
            uniqueness * 0.15 +
            validity * 0.2
        )
        
        details = {
            'total_records': len(original_data),
            'processed_records': len(processed_data),
            'data_retention_rate': completeness / 100,
            'quality_issues': []
        }
        
        # 添加质量问题
        if completeness < 90:
            details['quality_issues'].append('数据完整性较低')
        if accuracy < 80:
            details['quality_issues'].append('数据准确性需要改进')
        if consistency < 85:
            details['quality_issues'].append('数据一致性不足')
        if uniqueness < 95:
            details['quality_issues'].append('存在重复数据')
        if validity < 90:
            details['quality_issues'].append('数据格式有效性问题')
        
        return DataQualityScore(
            overall_score=round(overall_score, 1),
            completeness=round(completeness, 1),
            accuracy=round(accuracy, 1),
            consistency=round(consistency, 1),
            uniqueness=round(uniqueness, 1),
            validity=round(validity, 1),
            details=details
        )
    
    def _calculate_accuracy_score(self, data: List[Dict[str, Any]]) -> float:
        """计算准确性评分"""
        if not data:
            return 0.0
        
        # 基于数据类型匹配度计算
        total_fields = 0
        accurate_fields = 0
        
        for record in data:
            for field, value in record.items():
                total_fields += 1
                if self._is_accurate_value(field, value):
                    accurate_fields += 1
        
        return (accurate_fields / total_fields * 100) if total_fields > 0 else 0.0
    
    def _calculate_consistency_score(self, data: List[Dict[str, Any]]) -> float:
        """计算一致性评分"""
        if not data:
            return 0.0
        
        # 检查字段名一致性
        field_names = set()
        for record in data:
            field_names.update(record.keys())
        
        # 检查字段名标准化程度
        standardized_count = 0
        for field in field_names:
            if self._is_standardized_field_name(field):
                standardized_count += 1
        
        return (standardized_count / len(field_names) * 100) if field_names else 0.0
    
    def _calculate_uniqueness_score(self, data: List[Dict[str, Any]]) -> float:
        """计算唯一性评分"""
        if not data:
            return 100.0
        
        unique_hashes = set()
        for record in data:
            record_hash = self._calculate_record_hash(record)
            unique_hashes.add(record_hash)
        
        return (len(unique_hashes) / len(data) * 100)
    
    def _calculate_validity_score(self, data: List[Dict[str, Any]]) -> float:
        """计算有效性评分"""
        if not data:
            return 0.0
        
        total_values = 0
        valid_values = 0
        
        for record in data:
            for field, value in record.items():
                total_values += 1
                if self._is_valid_value(field, value):
                    valid_values += 1
        
        return (valid_values / total_values * 100) if total_values > 0 else 0.0
    
    def _is_accurate_value(self, field: str, value: Any) -> bool:
        """检查值是否准确"""
        if value is None:
            return False
        
        str_value = str(value).strip()
        if not str_value:
            return False
        
        field_lower = field.lower()
        
        # 根据字段类型检查准确性
        if 'email' in field_lower:
            return re.match(self.cleaning_rules['email'], str_value) is not None
        elif 'phone' in field_lower:
            return re.match(self.cleaning_rules['phone'], str_value) is not None
        elif 'url' in field_lower:
            return re.match(self.cleaning_rules['url'], str_value) is not None
        elif 'date' in field_lower:
            return re.match(self.cleaning_rules['date'], str_value) is not None
        
        return True  # 默认认为准确
    
    def _is_standardized_field_name(self, field_name: str) -> bool:
        """检查字段名是否标准化"""
        # 检查是否为小写、下划线分隔的格式
        return re.match(r'^[a-z][a-z0-9_]*$', field_name) is not None
    
    def _is_valid_value(self, field: str, value: Any) -> bool:
        """检查值是否有效"""
        if value is None:
            return False
        
        str_value = str(value).strip()
        return len(str_value) > 0


class IncrementalUpdateManager:
    """增量更新管理器"""

    def __init__(self):
        self.update_strategies = {
            'timestamp': self._timestamp_based_update,
            'hash': self._hash_based_update,
            'version': self._version_based_update
        }

    def detect_changes(
        self,
        old_data: List[Dict[str, Any]],
        new_data: List[Dict[str, Any]],
        strategy: str = 'hash',
        key_field: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """检测数据变化"""
        if strategy not in self.update_strategies:
            raise ValueError(f"不支持的更新策略: {strategy}")

        return self.update_strategies[strategy](old_data, new_data, key_field)

    def _timestamp_based_update(
        self,
        old_data: List[Dict[str, Any]],
        new_data: List[Dict[str, Any]],
        timestamp_field: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """基于时间戳的增量更新"""
        timestamp_field = timestamp_field or 'updated_at'

        # 获取最新的时间戳
        latest_timestamp = None
        if old_data:
            timestamps = []
            for record in old_data:
                if timestamp_field in record:
                    try:
                        ts = datetime.fromisoformat(str(record[timestamp_field]))
                        timestamps.append(ts)
                    except:
                        continue

            if timestamps:
                latest_timestamp = max(timestamps)

        # 筛选新数据
        added = []
        updated = []

        for record in new_data:
            if timestamp_field not in record:
                added.append(record)
                continue

            try:
                record_ts = datetime.fromisoformat(str(record[timestamp_field]))
                if latest_timestamp is None or record_ts > latest_timestamp:
                    added.append(record)
                else:
                    updated.append(record)
            except:
                added.append(record)

        return {
            'added': added,
            'updated': updated,
            'deleted': []  # 基于时间戳无法检测删除
        }

    def _hash_based_update(
        self,
        old_data: List[Dict[str, Any]],
        new_data: List[Dict[str, Any]],
        key_field: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """基于哈希的增量更新"""
        processor = DataProcessor()

        # 计算旧数据哈希
        old_hashes = {}
        for record in old_data:
            record_hash = processor._calculate_record_hash(record)
            if key_field and key_field in record:
                key = record[key_field]
                old_hashes[key] = record_hash
            else:
                old_hashes[record_hash] = record

        # 分析新数据
        added = []
        updated = []
        new_hashes = set()

        for record in new_data:
            record_hash = processor._calculate_record_hash(record)
            new_hashes.add(record_hash)

            if key_field and key_field in record:
                key = record[key_field]
                if key in old_hashes:
                    if old_hashes[key] != record_hash:
                        updated.append(record)
                else:
                    added.append(record)
            else:
                if record_hash not in old_hashes:
                    added.append(record)

        # 检测删除的记录
        deleted = []
        if key_field:
            new_keys = {record[key_field] for record in new_data if key_field in record}
            old_keys = set(old_hashes.keys())
            deleted_keys = old_keys - new_keys

            for record in old_data:
                if key_field in record and record[key_field] in deleted_keys:
                    deleted.append(record)

        return {
            'added': added,
            'updated': updated,
            'deleted': deleted
        }

    def _version_based_update(
        self,
        old_data: List[Dict[str, Any]],
        new_data: List[Dict[str, Any]],
        version_field: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """基于版本号的增量更新"""
        version_field = version_field or 'version'

        # 构建旧数据版本映射
        old_versions = {}
        for record in old_data:
            if version_field in record:
                key = self._get_record_key(record, version_field)
                version = record[version_field]
                if key not in old_versions or version > old_versions[key]['version']:
                    old_versions[key] = {
                        'version': version,
                        'record': record
                    }

        # 分析新数据
        added = []
        updated = []

        for record in new_data:
            if version_field not in record:
                added.append(record)
                continue

            key = self._get_record_key(record, version_field)
            new_version = record[version_field]

            if key in old_versions:
                if new_version > old_versions[key]['version']:
                    updated.append(record)
            else:
                added.append(record)

        return {
            'added': added,
            'updated': updated,
            'deleted': []  # 基于版本号无法检测删除
        }

    def _get_record_key(self, record: Dict[str, Any], exclude_field: str) -> str:
        """获取记录的唯一键（排除指定字段）"""
        key_data = {k: v for k, v in record.items() if k != exclude_field}
        return str(sorted(key_data.items()))
