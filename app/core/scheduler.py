from celery import Celery
from celery.schedules import crontab
from typing import Dict, Any, Optional
import os
import json
from datetime import datetime

from app.utils.logger import get_logger

logger = get_logger(__name__)

# Celery配置
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# 创建Celery应用
celery_app = Celery(
    "loop_hole",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=["app.core.tasks"]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # 结果过期时间
    result_expires=3600,
    
    # 任务路由
    task_routes={
        "app.core.tasks.extract_data": {"queue": "extraction"},
        "app.core.tasks.analyze_page": {"queue": "analysis"},
        "app.core.tasks.cleanup_old_results": {"queue": "maintenance"},
    },
    
    # 工作进程配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # 定时任务
    beat_schedule={
        "cleanup-old-results": {
            "task": "app.core.tasks.cleanup_old_results",
            "schedule": crontab(hour=2, minute=0),  # 每天凌晨2点执行
        },
        "health-check": {
            "task": "app.core.tasks.system_health_check",
            "schedule": 300.0,  # 每5分钟执行一次
        },
    },
)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.celery = celery_app
    
    def schedule_extraction_task(
        self,
        task_id: str,
        url: str,
        extraction_rules: Dict[str, Any],
        auth_config: Optional[Dict[str, Any]] = None,
        schedule_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        调度数据提取任务
        
        Args:
            task_id: 任务ID
            url: 目标URL
            extraction_rules: 提取规则
            auth_config: 认证配置
            schedule_config: 调度配置
            
        Returns:
            作业ID
        """
        from app.core.tasks import extract_data
        
        task_data = {
            "task_id": task_id,
            "url": url,
            "extraction_rules": extraction_rules,
            "auth_config": auth_config or {},
            "scheduled_at": datetime.utcnow().isoformat()
        }
        
        if schedule_config and schedule_config.get("type") == "delayed":
            # 延迟执行
            delay_seconds = schedule_config.get("delay_seconds", 0)
            job = extract_data.apply_async(
                args=[task_data],
                countdown=delay_seconds,
                queue="extraction"
            )
        elif schedule_config and schedule_config.get("type") == "scheduled":
            # 定时执行
            eta = schedule_config.get("eta")
            if eta:
                job = extract_data.apply_async(
                    args=[task_data],
                    eta=datetime.fromisoformat(eta),
                    queue="extraction"
                )
            else:
                job = extract_data.apply_async(
                    args=[task_data],
                    queue="extraction"
                )
        else:
            # 立即执行
            job = extract_data.apply_async(
                args=[task_data],
                queue="extraction"
            )
        
        logger.info(f"Scheduled extraction task {task_id} with job ID {job.id}")
        return job.id
    
    def schedule_page_analysis(
        self,
        url: str,
        analysis_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        调度页面分析任务
        
        Args:
            url: 目标URL
            analysis_config: 分析配置
            
        Returns:
            作业ID
        """
        from app.core.tasks import analyze_page
        
        task_data = {
            "url": url,
            "analysis_config": analysis_config or {},
            "scheduled_at": datetime.utcnow().isoformat()
        }
        
        job = analyze_page.apply_async(
            args=[task_data],
            queue="analysis"
        )
        
        logger.info(f"Scheduled page analysis for {url} with job ID {job.id}")
        return job.id
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        获取作业状态
        
        Args:
            job_id: 作业ID
            
        Returns:
            作业状态信息
        """
        try:
            job = self.celery.AsyncResult(job_id)
            
            status_info = {
                "job_id": job_id,
                "status": job.status,
                "result": None,
                "error": None,
                "traceback": None
            }
            
            if job.successful():
                status_info["result"] = job.result
            elif job.failed():
                status_info["error"] = str(job.info)
                status_info["traceback"] = job.traceback
            elif job.status == "PENDING":
                status_info["result"] = "Task is waiting to be processed"
            
            return status_info
            
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return {
                "job_id": job_id,
                "status": "ERROR",
                "error": str(e)
            }
    
    def cancel_job(self, job_id: str) -> bool:
        """
        取消作业
        
        Args:
            job_id: 作业ID
            
        Returns:
            是否成功取消
        """
        try:
            job = self.celery.AsyncResult(job_id)
            job.revoke(terminate=True)
            
            logger.info(f"Cancelled job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            inspect = self.celery.control.inspect()
            
            # 获取活跃任务
            active_tasks = inspect.active()
            
            # 获取预定任务
            scheduled_tasks = inspect.scheduled()
            
            # 获取保留任务
            reserved_tasks = inspect.reserved()
            
            return {
                "active": active_tasks or {},
                "scheduled": scheduled_tasks or {},
                "reserved": reserved_tasks or {},
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get queue status: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def setup_periodic_task(
        self,
        task_name: str,
        task_func: str,
        schedule: Dict[str, Any],
        args: list = None,
        kwargs: dict = None
    ):
        """
        设置周期性任务
        
        Args:
            task_name: 任务名称
            task_func: 任务函数路径
            schedule: 调度配置
            args: 任务参数
            kwargs: 任务关键字参数
        """
        from celery.schedules import crontab
        
        schedule_type = schedule.get("type", "crontab")
        
        if schedule_type == "crontab":
            # Crontab调度
            cron_schedule = crontab(
                minute=schedule.get("minute", "*"),
                hour=schedule.get("hour", "*"),
                day_of_week=schedule.get("day_of_week", "*"),
                day_of_month=schedule.get("day_of_month", "*"),
                month_of_year=schedule.get("month_of_year", "*")
            )
        elif schedule_type == "interval":
            # 间隔调度
            interval_seconds = schedule.get("seconds", 60)
            cron_schedule = interval_seconds
        else:
            raise ValueError(f"Unsupported schedule type: {schedule_type}")
        
        # 动态添加到beat_schedule
        self.celery.conf.beat_schedule[task_name] = {
            "task": task_func,
            "schedule": cron_schedule,
            "args": args or [],
            "kwargs": kwargs or {}
        }
        
        logger.info(f"Setup periodic task: {task_name}")

# 全局调度器实例
scheduler = TaskScheduler()
