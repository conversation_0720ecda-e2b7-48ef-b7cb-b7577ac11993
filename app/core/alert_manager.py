"""告警管理器"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import psutil

from app.core.websocket_manager import notifier
from app.utils.logger import get_logger
from app.utils.metrics import performance_monitor

logger = get_logger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class AlertRule:
    """告警规则"""
    name: str
    description: str
    metric: str
    threshold: float
    operator: str  # '>', '<', '>=', '<=', '==', '!='
    level: AlertLevel
    duration: int = 60  # 持续时间（秒）
    enabled: bool = True
    
class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.rules: List[AlertRule] = []
        self.active_alerts: Dict[str, Dict] = {}
        self.alert_history: List[Dict] = []
        self.running = False
        self._init_default_rules()
    
    def _init_default_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU使用率过高",
                metric="cpu_percent",
                threshold=80.0,
                operator=">",
                level=AlertLevel.WARNING,
                duration=120
            ),
            AlertRule(
                name="critical_cpu_usage",
                description="CPU使用率严重过高",
                metric="cpu_percent",
                threshold=95.0,
                operator=">",
                level=AlertLevel.CRITICAL,
                duration=60
            ),
            AlertRule(
                name="high_memory_usage",
                description="内存使用率过高",
                metric="memory_percent",
                threshold=85.0,
                operator=">",
                level=AlertLevel.WARNING,
                duration=180
            ),
            AlertRule(
                name="critical_memory_usage",
                description="内存使用率严重过高",
                metric="memory_percent",
                threshold=95.0,
                operator=">",
                level=AlertLevel.CRITICAL,
                duration=60
            ),
            AlertRule(
                name="high_disk_usage",
                description="磁盘使用率过高",
                metric="disk_percent",
                threshold=90.0,
                operator=">",
                level=AlertLevel.WARNING,
                duration=300
            ),
            AlertRule(
                name="slow_response_time",
                description="响应时间过慢",
                metric="avg_response_time",
                threshold=5.0,
                operator=">",
                level=AlertLevel.WARNING,
                duration=120
            ),
            AlertRule(
                name="high_error_rate",
                description="错误率过高",
                metric="error_rate",
                threshold=10.0,
                operator=">",
                level=AlertLevel.ERROR,
                duration=60
            ),
            AlertRule(
                name="task_queue_backlog",
                description="任务队列积压",
                metric="pending_tasks",
                threshold=100,
                operator=">",
                level=AlertLevel.WARNING,
                duration=300
            )
        ]
        
        self.rules.extend(default_rules)
        logger.info(f"Initialized {len(default_rules)} default alert rules")
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules.append(rule)
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        self.rules = [r for r in self.rules if r.name != rule_name]
        # 清除相关的活跃告警
        if rule_name in self.active_alerts:
            del self.active_alerts[rule_name]
        logger.info(f"Removed alert rule: {rule_name}")
    
    def get_rules(self) -> List[AlertRule]:
        """获取所有告警规则"""
        return self.rules.copy()
    
    def enable_rule(self, rule_name: str):
        """启用告警规则"""
        for rule in self.rules:
            if rule.name == rule_name:
                rule.enabled = True
                logger.info(f"Enabled alert rule: {rule_name}")
                break
    
    def disable_rule(self, rule_name: str):
        """禁用告警规则"""
        for rule in self.rules:
            if rule.name == rule_name:
                rule.enabled = False
                # 清除相关的活跃告警
                if rule_name in self.active_alerts:
                    del self.active_alerts[rule_name]
                logger.info(f"Disabled alert rule: {rule_name}")
                break
    
    async def check_alerts(self):
        """检查告警条件"""
        try:
            # 获取当前系统指标
            metrics = self._get_current_metrics()
            
            for rule in self.rules:
                if not rule.enabled:
                    continue
                
                # 检查规则条件
                if await self._evaluate_rule(rule, metrics):
                    await self._trigger_alert(rule, metrics)
                else:
                    await self._resolve_alert(rule)
                    
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
    
    def _get_current_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        try:
            # 系统指标
            system_info = performance_monitor.get_system_info()
            request_stats = performance_monitor.get_request_stats()
            
            metrics = {
                'cpu_percent': system_info.get('cpu_percent', 0),
                'memory_percent': system_info.get('memory', {}).get('percent', 0),
                'disk_percent': system_info.get('disk', {}).get('percent', 0),
                'avg_response_time': request_stats.get('avg_response_time', 0),
                'error_rate': request_stats.get('error_rate', 0),
                'pending_tasks': 0,  # TODO: 从任务队列获取
                'timestamp': datetime.now()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return {}
    
    async def _evaluate_rule(self, rule: AlertRule, metrics: Dict[str, Any]) -> bool:
        """评估告警规则"""
        try:
            metric_value = metrics.get(rule.metric)
            if metric_value is None:
                return False
            
            # 评估条件
            if rule.operator == '>':
                condition_met = metric_value > rule.threshold
            elif rule.operator == '<':
                condition_met = metric_value < rule.threshold
            elif rule.operator == '>=':
                condition_met = metric_value >= rule.threshold
            elif rule.operator == '<=':
                condition_met = metric_value <= rule.threshold
            elif rule.operator == '==':
                condition_met = metric_value == rule.threshold
            elif rule.operator == '!=':
                condition_met = metric_value != rule.threshold
            else:
                logger.warning(f"Unknown operator: {rule.operator}")
                return False
            
            if condition_met:
                # 检查持续时间
                now = datetime.now()
                if rule.name in self.active_alerts:
                    alert_info = self.active_alerts[rule.name]
                    if (now - alert_info['first_triggered']).total_seconds() >= rule.duration:
                        return True
                else:
                    # 首次触发，记录时间
                    self.active_alerts[rule.name] = {
                        'first_triggered': now,
                        'rule': rule,
                        'triggered': False
                    }
            else:
                # 条件不满足，清除记录
                if rule.name in self.active_alerts:
                    del self.active_alerts[rule.name]
            
            return False
            
        except Exception as e:
            logger.error(f"Error evaluating rule {rule.name}: {e}")
            return False
    
    async def _trigger_alert(self, rule: AlertRule, metrics: Dict[str, Any]):
        """触发告警"""
        try:
            if rule.name in self.active_alerts and self.active_alerts[rule.name].get('triggered'):
                return  # 已经触发过了
            
            # 标记为已触发
            if rule.name in self.active_alerts:
                self.active_alerts[rule.name]['triggered'] = True
            
            # 创建告警消息
            metric_value = metrics.get(rule.metric, 'N/A')
            alert_message = {
                'rule_name': rule.name,
                'description': rule.description,
                'level': rule.level.value,
                'metric': rule.metric,
                'current_value': metric_value,
                'threshold': rule.threshold,
                'operator': rule.operator,
                'timestamp': datetime.now().isoformat()
            }
            
            # 记录告警历史
            self.alert_history.append(alert_message.copy())
            
            # 限制历史记录数量
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
            
            # 发送WebSocket通知
            await notifier.notify_system_alert(
                alert_type=rule.name,
                message=f"{rule.description}: {metric_value} {rule.operator} {rule.threshold}",
                severity=rule.level.value
            )
            
            logger.warning(
                f"Alert triggered: {rule.name} - {rule.description}",
                extra=alert_message
            )
            
        except Exception as e:
            logger.error(f"Error triggering alert {rule.name}: {e}")
    
    async def _resolve_alert(self, rule: AlertRule):
        """解决告警"""
        try:
            if rule.name in self.active_alerts and self.active_alerts[rule.name].get('triggered'):
                # 发送解决通知
                await notifier.notify_system_alert(
                    alert_type=rule.name,
                    message=f"{rule.description} 已恢复正常",
                    severity="info"
                )
                
                logger.info(f"Alert resolved: {rule.name}")
                
                # 清除活跃告警
                del self.active_alerts[rule.name]
                
        except Exception as e:
            logger.error(f"Error resolving alert {rule.name}: {e}")
    
    async def start_monitoring(self):
        """开始监控"""
        if self.running:
            return
        
        self.running = True
        logger.info("Alert manager started")
        
        while self.running:
            try:
                await self.check_alerts()
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("Alert manager stopped")
    
    def get_active_alerts(self) -> List[Dict]:
        """获取活跃告警"""
        return [
            {
                'rule_name': name,
                'description': info['rule'].description,
                'level': info['rule'].level.value,
                'first_triggered': info['first_triggered'].isoformat(),
                'triggered': info.get('triggered', False)
            }
            for name, info in self.active_alerts.items()
        ]
    
    def get_alert_history(self, limit: int = 100) -> List[Dict]:
        """获取告警历史"""
        return self.alert_history[-limit:]
    
    def clear_alert_history(self):
        """清除告警历史"""
        self.alert_history.clear()
        logger.info("Alert history cleared")

# 全局告警管理器实例
alert_manager = AlertManager()