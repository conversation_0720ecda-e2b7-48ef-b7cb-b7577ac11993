"""
自定义异常类
"""

class LoopHoleException(Exception):
    """Loop Hole 基础异常"""
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class ExtractionException(LoopHoleException):
    """数据提取异常"""
    pass

class AnalysisException(LoopHoleException):
    """页面分析异常"""
    pass

class AuthenticationException(LoopHoleException):
    """认证异常"""
    pass

class ValidationException(LoopHoleException):
    """数据验证异常"""
    pass

class CacheException(LoopHoleException):
    """缓存异常"""
    pass

class TaskException(LoopHoleException):
    """任务执行异常"""
    pass

class BrowserException(LoopHoleException):
    """浏览器操作异常"""
    pass

class ConfigurationException(LoopHoleException):
    """配置异常"""
    pass

class DatabaseException(LoopHoleException):
    """数据库异常"""
    pass
