from celery import current_task
from typing import Dict, Any
import asyncio
from datetime import datetime, timedelta, timezone
import traceback

from app.core.scheduler import celery_app
from app.core.extractor import create_extractor
from app.core.analyzer import PageAnalyzer
from app.database import SessionLocal
from app.models.task import ExtractionTask
from app.models.result import ExtractionResult
from app.utils.logger import get_logger
from app.core.websocket_manager import notifier
from app.core.config import settings

logger = get_logger(__name__)

# 同步版本的任务函数，供内存调度器使用
def extract_data_sync(task_id: str, url: str, extraction_rules: Dict[str, Any], auth_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    同步版本的数据提取任务
    """
    logger.info(f"Starting sync data extraction task {task_id} for URL: {url}")

    try:
        # 更新任务状态为运行中
        db = SessionLocal()
        task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
        if task:
            task.status = "running"
            task.last_run = datetime.now(timezone.utc)
            db.commit()

        # 执行数据提取
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_extract_data_async(url, extraction_rules, auth_config, task_id))
        finally:
            loop.close()

        # 更新任务状态为完成
        if task:
            task.status = "completed"
            task.last_success = datetime.now(timezone.utc)
            db.commit()

        db.close()
        logger.info(f"Sync data extraction task {task_id} completed successfully")
        return result

    except Exception as e:
        logger.error(f"Sync data extraction task {task_id} failed: {e}")

        # 更新任务状态为失败
        try:
            db = SessionLocal()
            task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
            db.commit()
            db.close()
        except Exception as db_error:
            logger.error(f"Failed to update task status: {db_error}")

        raise e

def analyze_page_sync(url: str, analysis_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    同步版本的页面分析任务
    """
    logger.info(f"Starting sync page analysis for URL: {url}")

    try:
        # 执行页面分析
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(_analyze_page_async(url, analysis_config or {}))
        finally:
            loop.close()

        logger.info(f"Sync page analysis for {url} completed successfully")
        return result

    except Exception as e:
        logger.error(f"Sync page analysis for {url} failed: {e}")
        raise e

@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def extract_data(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    数据提取任务

    Args:
        task_data: 任务数据

    Returns:
        提取结果
    """
    task_id = task_data.get("task_id")
    url = task_data.get("url")
    extraction_rules = task_data.get("extraction_rules", {})
    auth_config = task_data.get("auth_config")

    logger.info(f"Starting data extraction task {task_id} for URL: {url}")

    try:
        # 更新任务状态为运行中
        db = SessionLocal()
        task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
        if task:
            task.status = "running"
            task.last_run = datetime.now(timezone.utc)
            db.commit()

            # 发送WebSocket通知：任务开始运行
            try:
                asyncio.run(notifier.notify_task_status_change(
                    task_id, "running", {"message": "任务开始执行"}
                ))
            except Exception as notify_error:
                logger.warning(f"Failed to send WebSocket notification: {notify_error}")

        # 执行数据提取
        result = asyncio.run(_extract_data_async(
            url, extraction_rules, auth_config, task_id
        ))

        # 保存提取结果
        if task and result.get("status") == "success":
            extraction_result = ExtractionResult.create(
                db_session=db,
                task_id=task_id,
                extracted_data=result.get("data", {}),
                metadata=result.get("metadata", {}),
                record_count=result.get("metadata", {}).get("record_count", 0)
            )

            # 更新任务状态
            task.status = "completed"
            task.last_success = datetime.now(timezone.utc)
            task.total_runs += 1
            db.commit()

            logger.info(f"Data extraction task {task_id} completed successfully")

            # 发送WebSocket通知：任务完成
            try:
                record_count = result.get("metadata", {}).get("record_count", 0)
                asyncio.run(notifier.notify_extraction_result(
                    task_id, str(extraction_result.id), record_count, True
                ))
            except Exception as notify_error:
                logger.warning(f"Failed to send completion notification: {notify_error}")

        elif task:
            # 提取失败
            task.status = "failed"
            task.error_message = result.get("error", "Unknown error")
            task.total_runs += 1
            db.commit()

            logger.error(f"Data extraction task {task_id} failed: {task.error_message}")

            # 发送WebSocket通知：任务失败
            try:
                asyncio.run(notifier.notify_task_status_change(
                    task_id, "failed", {
                        "error": task.error_message,
                        "message": "任务执行失败"
                    }
                ))
            except Exception as notify_error:
                logger.warning(f"Failed to send failure notification: {notify_error}")

        db.close()
        return result

    except Exception as e:
        logger.error(f"Data extraction task {task_id} error: {e}")
        logger.error(traceback.format_exc())

        # 更新任务状态为失败
        try:
            db = SessionLocal()
            task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.total_runs += 1
                db.commit()
            db.close()
        except Exception as db_error:
            logger.error(f"Failed to update task status: {db_error}")

        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying task {task_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (2 ** self.request.retries))

        return {
            "status": "error",
            "error": str(e),
            "task_id": task_id
        }

async def _extract_data_async(
    url: str,
    extraction_rules: Dict[str, Any],
    auth_config: Dict[str, Any] = None,
    task_id: str = None
) -> Dict[str, Any]:
    """异步数据提取"""
    async with create_extractor() as extractor:
        return await extractor.extract_data(
            url=url,
            extraction_rules=extraction_rules,
            auth_config=auth_config
        )

@celery_app.task(bind=True)
def analyze_page(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    页面分析任务

    Args:
        task_data: 任务数据

    Returns:
        分析结果
    """
    url = task_data.get("url")
    analysis_config = task_data.get("analysis_config", {})

    logger.info(f"Starting page analysis for URL: {url}")

    try:
        # 执行页面分析
        result = asyncio.run(_analyze_page_async(url, analysis_config))

        logger.info(f"Page analysis completed for URL: {url}")
        return result

    except Exception as e:
        logger.error(f"Page analysis error for {url}: {e}")
        logger.error(traceback.format_exc())

        return {
            "status": "error",
            "error": str(e),
            "url": url
        }

async def _analyze_page_async(
    url: str,
    analysis_config: Dict[str, Any]
) -> Dict[str, Any]:
    """异步页面分析"""
    async with create_extractor() as extractor:
        # 获取页面HTML
        await extractor._navigate_to_page(url)
        html_content = await extractor.page.content()

        # 分析页面结构
        analyzer = PageAnalyzer()
        analysis_result = analyzer.analyze_page(html_content, url)

        return {
            "status": "success",
            "url": url,
            "analysis": {
                "tables": [
                    {
                        "selector": table.selector,
                        "headers": table.headers,
                        "row_count": table.row_count,
                        "confidence": table.confidence
                    } for table in analysis_result.tables
                ],
                "cards": [
                    {
                        "selector": card.selector,
                        "fields": card.fields,
                        "confidence": card.confidence
                    } for card in analysis_result.cards
                ],
                "suggested_rules": analysis_result.suggested_rules,
                "confidence_score": analysis_result.confidence_score
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task
def cleanup_old_results() -> Dict[str, Any]:
    """清理旧的提取结果"""
    logger.info("Starting cleanup of old extraction results")

    try:
        db = SessionLocal()

        # 删除30天前的结果
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)

        deleted_count = db.query(ExtractionResult).filter(
            ExtractionResult.created_at < cutoff_date
        ).delete()

        db.commit()
        db.close()

        logger.info(f"Cleaned up {deleted_count} old extraction results")

        return {
            "status": "success",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }

    except Exception as e:
        logger.error(f"Cleanup task error: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@celery_app.task
def system_health_check() -> Dict[str, Any]:
    """系统健康检查"""
    logger.info("Performing system health check")

    try:
        health_status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database": False,
            "redis": False,
            "celery": True  # 如果任务能执行，说明Celery正常
        }

        # 检查数据库连接
        try:
            db = SessionLocal()
            db.execute("SELECT 1")
            db.close()
            health_status["database"] = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")

        # 检查Redis连接
        try:
            from redis import Redis
            redis_client = Redis.from_url(settings.REDIS_URL)
            redis_client.ping()
            health_status["redis"] = True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")

        # 计算整体健康状态
        all_healthy = all(health_status[key] for key in ["database", "redis", "celery"])
        health_status["overall"] = "healthy" if all_healthy else "unhealthy"

        if not all_healthy:
            logger.warning(f"System health check failed: {health_status}")

        return health_status

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

@celery_app.task
def process_scheduled_tasks() -> Dict[str, Any]:
    """处理预定的任务"""
    logger.info("Processing scheduled tasks")

    try:
        db = SessionLocal()

        # 查找需要执行的任务
        now = datetime.now(timezone.utc)
        scheduled_tasks = db.query(ExtractionTask).filter(
            ExtractionTask.status == "active",
            ExtractionTask.next_run <= now
        ).all()

        processed_count = 0

        for task in scheduled_tasks:
            try:
                # 调度任务执行
                USE_MEMORY_SCHEDULER = settings.USE_MEMORY_SCHEDULER

                if USE_MEMORY_SCHEDULER:
                    from app.core.memory_scheduler import memory_scheduler as scheduler
                else:
                    from app.core.scheduler import scheduler
                job_id = scheduler.schedule_extraction_task(
                    task_id=str(task.id),
                    url=task.url,
                    extraction_rules=task.extraction_rules,
                    auth_config=task.auth_config
                )

                # 更新下次运行时间
                if task.schedule_config:
                    schedule_type = task.schedule_config.get("type", "once")
                    if schedule_type == "interval":
                        interval_minutes = task.schedule_config.get("interval_minutes", 60)
                        task.next_run = now + timedelta(minutes=interval_minutes)
                    elif schedule_type == "daily":
                        task.next_run = now + timedelta(days=1)
                    elif schedule_type == "weekly":
                        task.next_run = now + timedelta(weeks=1)
                    else:
                        # 一次性任务，设置为非活跃
                        task.status = "completed"

                processed_count += 1
                logger.info(f"Scheduled task {task.id} with job ID {job_id}")

            except Exception as e:
                logger.error(f"Failed to schedule task {task.id}: {e}")
                task.status = "failed"
                task.error_message = str(e)

        db.commit()
        db.close()

        logger.info(f"Processed {processed_count} scheduled tasks")

        return {
            "status": "success",
            "processed_count": processed_count,
            "timestamp": now.isoformat()
        }

    except Exception as e:
        logger.error(f"Process scheduled tasks error: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
