"""
监控系统核心模块
提供系统监控、性能指标收集、告警管理等功能
"""

import structlog
import psutil
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import json
import threading
from collections import defaultdict, deque

from prometheus_client import Counter, Histogram, Gauge
from app.utils.logger import get_logger
from app.core.alert_manager import AlertManager

logger = get_logger(__name__)
structured_logger = structlog.get_logger()

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_percent: float
    disk_used: int
    disk_total: int
    network_sent: int
    network_recv: int
    load_average: List[float]

@dataclass
class ApplicationMetrics:
    """应用指标"""
    timestamp: datetime
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_response_time: float
    error_rate: float
    cache_hit_rate: float
    database_connections: int

@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    source: str
    resolved: bool = False
    resolved_at: Optional[datetime] = None

class MonitoringSystem:
    """监控系统主类"""
    
    def __init__(self):
        self.alert_manager = AlertManager()
        self.metrics_history = deque(maxlen=1000)  # 保留最近1000个指标点
        self.alerts = {}  # 活跃告警
        self.alert_rules = []  # 告警规则
        self.monitoring_enabled = True
        self.collection_interval = 30  # 秒
        
        # Prometheus指标
        self.system_cpu_usage = Gauge('system_cpu_usage_percent', 'System CPU usage percentage')
        self.system_memory_usage = Gauge('system_memory_usage_bytes', 'System memory usage in bytes')
        self.system_disk_usage = Gauge('system_disk_usage_percent', 'System disk usage percentage')
        self.application_active_tasks = Gauge('app_active_tasks', 'Number of active tasks')
        self.application_error_rate = Gauge('app_error_rate', 'Application error rate')
        
        # 指标收集器
        self.extraction_counter = Counter('extractions_total', 'Total extractions', ['status'])
        self.extraction_duration = Histogram('extraction_duration_seconds', 'Extraction duration')
        self.api_request_counter = Counter('api_requests_total', 'Total API requests', ['method', 'endpoint', 'status'])
        self.api_request_duration = Histogram('api_request_duration_seconds', 'API request duration', ['method', 'endpoint'])
        
        # 启动监控线程
        self._start_monitoring_thread()
        
        # 注册默认告警规则
        self._register_default_alert_rules()
    
    def _start_monitoring_thread(self):
        """启动监控线程"""
        def monitoring_loop():
            while self.monitoring_enabled:
                try:
                    self._collect_metrics()
                    self._check_alert_rules()
                    time.sleep(self.collection_interval)
                except Exception as e:
                    logger.error(f"Monitoring loop error: {e}")
                    time.sleep(5)  # 错误时短暂等待
        
        thread = threading.Thread(target=monitoring_loop, daemon=True)
        thread.start()
        logger.info("Monitoring thread started")
    
    def _collect_metrics(self):
        """收集系统和应用指标"""
        try:
            # 收集系统指标
            system_metrics = self._collect_system_metrics()
            
            # 收集应用指标
            app_metrics = self._collect_application_metrics()
            
            # 更新Prometheus指标
            self.system_cpu_usage.set(system_metrics.cpu_percent)
            self.system_memory_usage.set(system_metrics.memory_used)
            self.system_disk_usage.set(system_metrics.disk_percent)
            self.application_active_tasks.set(app_metrics.active_tasks)
            self.application_error_rate.set(app_metrics.error_rate)
            
            # 保存到历史记录
            self.metrics_history.append({
                'system': asdict(system_metrics),
                'application': asdict(app_metrics)
            })
            
            # 结构化日志记录
            structured_logger.info(
                "metrics_collected",
                cpu_percent=system_metrics.cpu_percent,
                memory_percent=system_metrics.memory_percent,
                disk_percent=system_metrics.disk_percent,
                active_tasks=app_metrics.active_tasks,
                error_rate=app_metrics.error_rate
            )
            
        except Exception as e:
            logger.error(f"Failed to collect metrics: {e}")
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        
        return SystemMetrics(
            timestamp=datetime.utcnow(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used=memory.used,
            memory_total=memory.total,
            disk_percent=disk.percent,
            disk_used=disk.used,
            disk_total=disk.total,
            network_sent=network.bytes_sent,
            network_recv=network.bytes_recv,
            load_average=list(load_avg)
        )
    
    def _collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        # 这里需要从数据库或缓存中获取应用指标
        # 暂时使用模拟数据
        return ApplicationMetrics(
            timestamp=datetime.utcnow(),
            active_tasks=0,  # 需要从Celery获取
            completed_tasks=0,  # 需要从数据库获取
            failed_tasks=0,  # 需要从数据库获取
            avg_response_time=0.0,  # 需要从指标中计算
            error_rate=0.0,  # 需要从指标中计算
            cache_hit_rate=0.0,  # 需要从Redis获取
            database_connections=0  # 需要从数据库连接池获取
        )
    
    def _register_default_alert_rules(self):
        """注册默认告警规则"""
        # CPU使用率告警
        self.add_alert_rule(
            name="high_cpu_usage",
            condition=lambda metrics: metrics.get('system', {}).get('cpu_percent', 0) > 80,
            level=AlertLevel.WARNING,
            message="CPU使用率过高: {cpu_percent:.1f}%"
        )
        
        # 内存使用率告警
        self.add_alert_rule(
            name="high_memory_usage",
            condition=lambda metrics: metrics.get('system', {}).get('memory_percent', 0) > 85,
            level=AlertLevel.ERROR,
            message="内存使用率过高: {memory_percent:.1f}%"
        )
        
        # 磁盘使用率告警
        self.add_alert_rule(
            name="high_disk_usage",
            condition=lambda metrics: metrics.get('system', {}).get('disk_percent', 0) > 90,
            level=AlertLevel.CRITICAL,
            message="磁盘使用率过高: {disk_percent:.1f}%"
        )
        
        # 错误率告警
        self.add_alert_rule(
            name="high_error_rate",
            condition=lambda metrics: metrics.get('application', {}).get('error_rate', 0) > 0.05,
            level=AlertLevel.ERROR,
            message="应用错误率过高: {error_rate:.2%}"
        )
    
    def add_alert_rule(self, name: str, condition: Callable, level: AlertLevel, message: str):
        """添加告警规则"""
        self.alert_rules.append({
            'name': name,
            'condition': condition,
            'level': level,
            'message': message
        })
        logger.info(f"Added alert rule: {name}")
    
    def _check_alert_rules(self):
        """检查告警规则"""
        if not self.metrics_history:
            return
        
        latest_metrics = self.metrics_history[-1]
        
        for rule in self.alert_rules:
            try:
                if rule['condition'](latest_metrics):
                    self._trigger_alert(rule, latest_metrics)
                else:
                    self._resolve_alert(rule['name'])
            except Exception as e:
                logger.error(f"Error checking alert rule {rule['name']}: {e}")
    
    def _trigger_alert(self, rule: Dict, metrics: Dict):
        """触发告警"""
        alert_id = rule['name']
        
        # 如果告警已经存在且未解决，不重复触发
        if alert_id in self.alerts and not self.alerts[alert_id].resolved:
            return
        
        # 格式化消息
        try:
            message = rule['message'].format(**metrics.get('system', {}), **metrics.get('application', {}))
        except:
            message = rule['message']
        
        alert = Alert(
            id=alert_id,
            level=rule['level'],
            title=f"系统告警: {rule['name']}",
            message=message,
            timestamp=datetime.utcnow(),
            source="monitoring_system"
        )
        
        self.alerts[alert_id] = alert
        
        # 发送告警通知
        asyncio.create_task(self.alert_manager.send_alert(
            level=alert.level.value,
            title=alert.title,
            message=alert.message,
            source=alert.source
        ))
        
        # 结构化日志记录
        structured_logger.warning(
            "alert_triggered",
            alert_id=alert_id,
            level=alert.level.value,
            message=message
        )
        
        logger.warning(f"Alert triggered: {alert.title} - {alert.message}")
    
    def _resolve_alert(self, alert_id: str):
        """解决告警"""
        if alert_id in self.alerts and not self.alerts[alert_id].resolved:
            self.alerts[alert_id].resolved = True
            self.alerts[alert_id].resolved_at = datetime.utcnow()
            
            structured_logger.info(
                "alert_resolved",
                alert_id=alert_id
            )
            
            logger.info(f"Alert resolved: {alert_id}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        if not self.metrics_history:
            return {}
        return self.metrics_history[-1]
    
    def get_metrics_history(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """获取指标历史"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        filtered_metrics = []
        for metrics in self.metrics_history:
            system_time = datetime.fromisoformat(metrics['system']['timestamp'].replace('Z', '+00:00'))
            if system_time >= cutoff_time:
                filtered_metrics.append(metrics)
        
        return filtered_metrics
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self.alerts.values() if not alert.resolved]
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [
            alert for alert in self.alerts.values()
            if alert.timestamp >= cutoff_time
        ]
    
    def record_extraction(self, status: str, duration: float = 0, record_count: int = 0):
        """记录数据提取指标"""
        self.extraction_counter.labels(status=status).inc()
        if duration > 0:
            self.extraction_duration.observe(duration)
        
        structured_logger.info(
            "extraction_recorded",
            status=status,
            duration=duration,
            record_count=record_count
        )
    
    def record_api_request(self, method: str, endpoint: str, status: int, duration: float):
        """记录API请求指标"""
        self.api_request_counter.labels(method=method, endpoint=endpoint, status=str(status)).inc()
        self.api_request_duration.labels(method=method, endpoint=endpoint).observe(duration)
        
        structured_logger.info(
            "api_request_recorded",
            method=method,
            endpoint=endpoint,
            status=status,
            duration=duration
        )
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_enabled = False
        logger.info("Monitoring system stopped")

# 全局监控实例
monitoring_system = MonitoringSystem()
