"""
智能提取规则推荐器
基于页面分析结果，自动推荐最佳提取规则
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import re
import logging
from .analyzer import AnalysisResult, TableInfo, CardInfo, ElementInfo

logger = logging.getLogger(__name__)

@dataclass
class RuleRecommendation:
    """规则推荐结果"""
    rule_type: str  # 'table', 'card', 'list', 'form'
    selector: str
    name: str
    confidence: float
    description: str
    config: Dict[str, Any]
    priority: int  # 1-10, 10为最高优先级

@dataclass
class RecommendationResult:
    """推荐结果集合"""
    recommendations: List[RuleRecommendation]
    total_confidence: float
    suggested_config: Dict[str, Any]
    warnings: List[str]

class RuleRecommender:
    """智能提取规则推荐器"""
    
    def __init__(self):
        self.min_confidence_threshold = 0.6
        self.high_confidence_threshold = 0.8
        
        # 数据类型权重
        self.data_type_weights = {
            'number': 0.9,
            'decimal': 0.9,
            'currency': 1.0,
            'percentage': 0.9,
            'date': 0.8,
            'email': 0.7,
            'phone': 0.7,
            'text': 0.5
        }
        
        # 元素类型优先级
        self.element_priorities = {
            'table': 10,
            'card': 8,
            'list': 6,
            'form': 7
        }
    
    def recommend_rules(self, analysis_result: AnalysisResult) -> RecommendationResult:
        """基于页面分析结果推荐提取规则"""
        recommendations = []
        warnings = []
        
        try:
            # 推荐表格提取规则
            table_recommendations = self._recommend_table_rules(analysis_result.tables)
            recommendations.extend(table_recommendations)
            
            # 推荐卡片提取规则
            card_recommendations = self._recommend_card_rules(analysis_result.cards)
            recommendations.extend(card_recommendations)
            
            # 推荐列表提取规则
            list_recommendations = self._recommend_list_rules(analysis_result.lists)
            recommendations.extend(list_recommendations)
            
            # 按优先级和置信度排序
            recommendations.sort(key=lambda x: (x.priority, x.confidence), reverse=True)
            
            # 计算总体置信度
            total_confidence = self._calculate_total_confidence(recommendations)
            
            # 生成建议的配置
            suggested_config = self._generate_suggested_config(recommendations)
            
            # 生成警告信息
            warnings = self._generate_warnings(recommendations, analysis_result)
            
            logger.info(f"Generated {len(recommendations)} rule recommendations with confidence {total_confidence:.2f}")
            
        except Exception as e:
            logger.error(f"Error generating rule recommendations: {e}")
            warnings.append(f"推荐生成过程中出现错误: {str(e)}")
        
        return RecommendationResult(
            recommendations=recommendations,
            total_confidence=total_confidence,
            suggested_config=suggested_config,
            warnings=warnings
        )
    
    def _recommend_table_rules(self, tables: List[TableInfo]) -> List[RuleRecommendation]:
        """推荐表格提取规则"""
        recommendations = []
        
        for i, table in enumerate(tables):
            if table.confidence < self.min_confidence_threshold:
                continue
            
            # 分析表格数据类型
            data_types = self._analyze_table_data_types(table)
            
            # 计算推荐置信度
            confidence = self._calculate_table_confidence(table, data_types)
            
            # 生成规则配置
            config = {
                'selector': table.selector,
                'type': 'table',
                'headers': table.headers,
                'data_types': data_types,
                'row_count': table.row_count,
                'extract_headers': True,
                'skip_empty_rows': True
            }
            
            # 生成描述
            description = f"表格数据提取 - {len(table.headers)}列，约{table.row_count}行数据"
            if data_types:
                main_types = list(set(data_types.values()))[:3]
                description += f"，主要数据类型: {', '.join(main_types)}"
            
            recommendation = RuleRecommendation(
                rule_type='table',
                selector=table.selector,
                name=f'table_data_{i+1}',
                confidence=confidence,
                description=description,
                config=config,
                priority=self.element_priorities['table']
            )
            
            recommendations.append(recommendation)
        
        return recommendations
    
    def _recommend_card_rules(self, cards: List[CardInfo]) -> List[RuleRecommendation]:
        """推荐卡片提取规则"""
        recommendations = []
        
        for i, card in enumerate(cards):
            if card.confidence < self.min_confidence_threshold:
                continue
            
            # 分析卡片字段类型
            field_types = self._analyze_card_field_types(card)
            
            # 计算推荐置信度
            confidence = self._calculate_card_confidence(card, field_types)
            
            # 生成规则配置
            config = {
                'selector': card.selector,
                'type': 'card',
                'fields': card.fields,
                'field_types': field_types,
                'extract_all_fields': True,
                'group_by_card': True
            }
            
            # 生成描述
            description = f"卡片数据提取 - {len(card.fields)}个字段"
            if field_types:
                main_types = list(set(field_types.values()))[:3]
                description += f"，字段类型: {', '.join(main_types)}"
            
            recommendation = RuleRecommendation(
                rule_type='card',
                selector=card.selector,
                name=f'card_data_{i+1}',
                confidence=confidence,
                description=description,
                config=config,
                priority=self.element_priorities['card']
            )
            
            recommendations.append(recommendation)
        
        return recommendations
    
    def _recommend_list_rules(self, lists: List[ElementInfo]) -> List[RuleRecommendation]:
        """推荐列表提取规则"""
        recommendations = []
        
        for i, list_elem in enumerate(lists):
            if list_elem.confidence < self.min_confidence_threshold:
                continue
            
            # 计算推荐置信度
            confidence = list_elem.confidence
            
            # 生成规则配置
            config = {
                'selector': list_elem.selector,
                'type': 'list',
                'extract_text': True,
                'extract_links': True,
                'remove_duplicates': True
            }
            
            # 生成描述
            description = f"列表数据提取 - {list_elem.element_type}类型列表"
            
            recommendation = RuleRecommendation(
                rule_type='list',
                selector=list_elem.selector,
                name=f'list_data_{i+1}',
                confidence=confidence,
                description=description,
                config=config,
                priority=self.element_priorities['list']
            )
            
            recommendations.append(recommendation)
        
        return recommendations
    
    def _analyze_table_data_types(self, table: TableInfo) -> Dict[str, str]:
        """分析表格数据类型"""
        data_types = {}
        
        if not table.sample_data or not table.headers:
            return data_types
        
        for col_idx, header in enumerate(table.headers):
            column_values = []
            for row in table.sample_data:
                if col_idx < len(row):
                    column_values.append(row[col_idx])
            
            # 分析列数据类型
            detected_type = self._detect_data_type(column_values)
            data_types[header] = detected_type
        
        return data_types
    
    def _analyze_card_field_types(self, card: CardInfo) -> Dict[str, str]:
        """分析卡片字段类型"""
        field_types = {}
        
        if not card.sample_values or not card.fields:
            return field_types
        
        for i, field in enumerate(card.fields):
            if i < len(card.sample_values):
                value = card.sample_values[i]
                detected_type = self._detect_data_type([value])
                field_types[field] = detected_type
        
        return field_types
    
    def _detect_data_type(self, values: List[str]) -> str:
        """检测数据类型"""
        if not values:
            return 'text'
        
        # 统计各种数据类型的匹配数量
        type_counts = {}
        
        for value in values:
            if not value or not value.strip():
                continue
            
            value = value.strip()
            
            # 检查各种数据类型模式
            for data_type, pattern in {
                'number': r'^\d+$',
                'decimal': r'^\d+\.\d+$',
                'currency': r'^[\$¥€£]\d+(?:,\d{3})*(?:\.\d{2})?$',
                'percentage': r'^\d+(?:\.\d+)?%$',
                'date': r'^\d{4}-\d{2}-\d{2}$|^\d{2}/\d{2}/\d{4}$',
                'email': r'^[^@]+@[^@]+\.[^@]+$',
                'phone': r'^\+?\d{10,15}$'
            }.items():
                if re.match(pattern, value):
                    type_counts[data_type] = type_counts.get(data_type, 0) + 1
                    break
            else:
                type_counts['text'] = type_counts.get('text', 0) + 1
        
        # 返回最常见的数据类型
        if type_counts:
            return max(type_counts.items(), key=lambda x: x[1])[0]
        
        return 'text'
    
    def _calculate_table_confidence(self, table: TableInfo, data_types: Dict[str, str]) -> float:
        """计算表格推荐置信度"""
        base_confidence = table.confidence
        
        # 根据数据类型调整置信度
        if data_types:
            type_weights = [self.data_type_weights.get(dt, 0.5) for dt in data_types.values()]
            avg_type_weight = sum(type_weights) / len(type_weights)
            base_confidence = (base_confidence + avg_type_weight) / 2
        
        # 根据行数调整置信度
        if table.row_count > 10:
            base_confidence += 0.1
        elif table.row_count > 5:
            base_confidence += 0.05
        
        return min(base_confidence, 1.0)
    
    def _calculate_card_confidence(self, card: CardInfo, field_types: Dict[str, str]) -> float:
        """计算卡片推荐置信度"""
        base_confidence = card.confidence
        
        # 根据字段类型调整置信度
        if field_types:
            type_weights = [self.data_type_weights.get(ft, 0.5) for ft in field_types.values()]
            avg_type_weight = sum(type_weights) / len(type_weights)
            base_confidence = (base_confidence + avg_type_weight) / 2
        
        return min(base_confidence, 1.0)
    
    def _calculate_total_confidence(self, recommendations: List[RuleRecommendation]) -> float:
        """计算总体推荐置信度"""
        if not recommendations:
            return 0.0
        
        # 加权平均，高优先级的推荐权重更大
        total_weight = 0
        weighted_sum = 0
        
        for rec in recommendations:
            weight = rec.priority / 10.0
            weighted_sum += rec.confidence * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _generate_suggested_config(self, recommendations: List[RuleRecommendation]) -> Dict[str, Any]:
        """生成建议的提取配置"""
        if not recommendations:
            return {}
        
        # 选择最高优先级和置信度的推荐
        best_recommendations = [r for r in recommendations if r.confidence >= self.high_confidence_threshold]
        if not best_recommendations:
            best_recommendations = recommendations[:3]  # 取前3个
        
        config = {
            'extraction_rules': {},
            'settings': {
                'timeout': 30,
                'wait_for_load': True,
                'screenshot': False,
                'user_agent': 'Loop Hole Data Extractor'
            }
        }
        
        for rec in best_recommendations:
            config['extraction_rules'][rec.name] = rec.config
        
        return config
    
    def _generate_warnings(self, recommendations: List[RuleRecommendation], analysis_result: AnalysisResult) -> List[str]:
        """生成警告信息"""
        warnings = []
        
        # 检查是否有低置信度的推荐
        low_confidence_count = len([r for r in recommendations if r.confidence < self.high_confidence_threshold])
        if low_confidence_count > 0:
            warnings.append(f"有 {low_confidence_count} 个推荐的置信度较低，建议手动验证")
        
        # 检查是否没有找到高质量的数据结构
        if not recommendations:
            warnings.append("未找到明确的数据结构，建议手动配置提取规则")
        
        # 检查页面复杂度
        total_elements = len(analysis_result.tables) + len(analysis_result.cards) + len(analysis_result.lists)
        if total_elements > 10:
            warnings.append("页面结构较复杂，建议分批次提取数据")
        
        return warnings
