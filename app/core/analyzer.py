from bs4 import BeautifulSoup, Tag
from typing import List, Dict, Any, Optional
import re
import hashlib
from dataclasses import dataclass

@dataclass
class ElementInfo:
    selector: str
    element_type: str
    confidence: float
    attributes: Dict[str, Any]

@dataclass
class TableInfo(ElementInfo):
    headers: List[str]
    row_count: int
    sample_data: List[List[str]]

@dataclass
class CardInfo(ElementInfo):
    fields: List[str]
    sample_values: List[str]

@dataclass
class AnalysisResult:
    url: str
    tables: List[TableInfo]
    cards: List[CardInfo]
    lists: List[ElementInfo]
    suggested_rules: Dict[str, Any]
    confidence_score: int

class PageAnalyzer:
    def __init__(self):
        self.data_patterns = {
            'number': r'^\d+$',
            'decimal': r'^\d+\.\d+$',
            'currency': r'^[\$¥€£]\d+(?:,\d{3})*(?:\.\d{2})?$',
            'percentage': r'^\d+(?:\.\d+)?%$',
            'date': r'^\d{4}-\d{2}-\d{2}$|^\d{2}/\d{2}/\d{4}$',
            'email': r'^[^@]+@[^@]+\.[^@]+$',
            'phone': r'^\+?\d{10,15}$'
        }
        
        self.table_indicators = [
            'table', 'tbody', 'thead', 'tr', 'td', 'th'
        ]
        
        self.card_indicators = [
            'card', 'panel', 'box', 'item', 'tile', 'widget'
        ]
    
    def analyze_page(self, html: str, url: str = "") -> AnalysisResult:
        """分析页面HTML，识别数据结构"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 识别表格
        tables = self._analyze_tables(soup)
        
        # 识别卡片
        cards = self._analyze_cards(soup)
        
        # 识别列表
        lists = self._analyze_lists(soup)
        
        # 生成建议的提取规则
        suggested_rules = self._generate_suggested_rules(tables, cards, lists)
        
        # 计算整体置信度
        confidence_score = self._calculate_confidence(tables, cards, lists)
        
        return AnalysisResult(
            url=url,
            tables=tables,
            cards=cards,
            lists=lists,
            suggested_rules=suggested_rules,
            confidence_score=confidence_score
        )
    
    def _analyze_tables(self, soup: BeautifulSoup) -> List[TableInfo]:
        """识别和分析表格"""
        tables = []
        
        # 查找标准HTML表格
        for table in soup.find_all('table'):
            table_info = self._extract_table_info(table)
            if table_info:
                tables.append(table_info)
        
        # 查找类似表格的div结构
        potential_tables = soup.find_all('div', class_=re.compile(r'table|grid|data'))
        for div in potential_tables:
            table_info = self._extract_div_table_info(div)
            if table_info:
                tables.append(table_info)
        
        return tables
    
    def _extract_table_info(self, table: Tag) -> Optional[TableInfo]:
        """从HTML table元素提取信息"""
        try:
            # 提取表头
            headers = []
            header_row = table.find('thead')
            if header_row:
                headers = [th.get_text().strip() for th in header_row.find_all(['th', 'td'])]
            else:
                # 如果没有thead，尝试第一行
                first_row = table.find('tr')
                if first_row:
                    headers = [th.get_text().strip() for th in first_row.find_all(['th', 'td'])]
            
            # 提取数据行
            rows = []
            tbody = table.find('tbody') or table
            for tr in tbody.find_all('tr')[1 if not table.find('thead') else 0:]:
                row_data = [td.get_text().strip() for td in tr.find_all(['td', 'th'])]
                if row_data:
                    rows.append(row_data)
            
            if not headers or not rows:
                return None
            
            # 生成选择器
            selector = self._generate_element_selector(table)
            
            # 分析数据类型
            confidence = self._calculate_table_confidence(headers, rows)
            
            return TableInfo(
                selector=selector,
                element_type='table',
                confidence=confidence,
                attributes={'tag': 'table'},
                headers=headers,
                row_count=len(rows),
                sample_data=rows[:5]  # 只保存前5行作为样本
            )
        
        except Exception:
            return None
    
    def _extract_div_table_info(self, div: Tag) -> Optional[TableInfo]:
        """从div结构中提取类似表格的信息"""
        try:
            # 查找行元素
            row_selectors = ['div[class*="row"]', 'div[class*="item"]', 'li']
            rows = []
            
            for selector in row_selectors:
                potential_rows = div.select(selector)
                if len(potential_rows) >= 3:  # 至少3行才认为是表格
                    rows = potential_rows
                    break
            
            if not rows:
                return None
            
            # 分析第一行获取列结构
            first_row = rows[0]
            columns = first_row.find_all(['div', 'span', 'p'])
            
            if len(columns) < 2:  # 至少2列
                return None
            
            # 提取表头（假设第一行是表头或从类名推断）
            headers = []
            for col in columns:
                text = col.get_text().strip()
                if text:
                    headers.append(text)
                else:
                    headers.append(f"Column_{len(headers) + 1}")
            
            # 提取样本数据
            sample_data = []
            for row in rows[1:6]:  # 跳过表头，取5行样本
                row_data = []
                row_columns = row.find_all(['div', 'span', 'p'])
                for col in row_columns[:len(headers)]:
                    row_data.append(col.get_text().strip())
                if row_data:
                    sample_data.append(row_data)
            
            selector = self._generate_element_selector(div)
            confidence = self._calculate_table_confidence(headers, sample_data)
            
            return TableInfo(
                selector=selector,
                element_type='div_table',
                confidence=confidence,
                attributes={'tag': 'div', 'class': div.get('class', [])},
                headers=headers,
                row_count=len(rows) - 1,
                sample_data=sample_data
            )
        
        except Exception:
            return None
    
    def _analyze_cards(self, soup: BeautifulSoup) -> List[CardInfo]:
        """识别和分析卡片组件"""
        cards = []
        
        # 查找卡片容器
        card_selectors = [
            'div[class*="card"]',
            'div[class*="panel"]',
            'div[class*="box"]',
            'div[class*="item"]',
            'div[class*="metric"]',
            'div[class*="stat"]'
        ]
        
        for selector in card_selectors:
            potential_cards = soup.select(selector)
            
            # 如果找到多个相似的元素，可能是卡片列表
            if len(potential_cards) >= 2:
                card_info = self._extract_card_info(potential_cards[0], len(potential_cards))
                if card_info:
                    cards.append(card_info)
        
        return cards
    
    def _extract_card_info(self, card: Tag, count: int) -> Optional[CardInfo]:
        """从卡片元素提取信息"""
        try:
            # 提取文本内容
            text_elements = card.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'])
            
            fields = []
            sample_values = []
            
            for element in text_elements:
                text = element.get_text().strip()
                if text and len(text) < 100:  # 过滤太长的文本
                    # 判断是标签还是值
                    if self._is_label(element, text):
                        fields.append(text)
                    else:
                        sample_values.append(text)
            
            if not fields and not sample_values:
                return None
            
            # 如果没有明确的字段名，根据内容推断
            if not fields:
                fields = [f"Field_{i+1}" for i in range(min(len(sample_values), 3))]
            
            selector = self._generate_element_selector(card)
            confidence = self._calculate_card_confidence(fields, sample_values, count)
            
            return CardInfo(
                selector=selector,
                element_type='card',
                confidence=confidence,
                attributes={'tag': card.name, 'class': card.get('class', [])},
                fields=fields[:5],  # 最多5个字段
                sample_values=sample_values[:5]
            )
        
        except Exception:
            return None
    
    def _analyze_lists(self, soup: BeautifulSoup) -> List[ElementInfo]:
        """识别列表结构"""
        lists = []
        
        # HTML列表
        for ul in soup.find_all(['ul', 'ol']):
            items = ul.find_all('li')
            if len(items) >= 3:
                selector = self._generate_element_selector(ul)
                lists.append(ElementInfo(
                    selector=selector,
                    element_type='list',
                    confidence=0.8,
                    attributes={'tag': ul.name, 'item_count': len(items)}
                ))
        
        return lists
    
    def _generate_element_selector(self, element: Tag) -> str:
        """为元素生成CSS选择器"""
        selectors = []
        
        # ID选择器优先级最高
        if element.get('id'):
            return f"#{element['id']}"
        
        # 类选择器
        if element.get('class'):
            classes = element['class']
            if isinstance(classes, list):
                class_selector = '.' + '.'.join(classes)
                selectors.append(f"{element.name}{class_selector}")
        
        # 属性选择器
        for attr in ['data-id', 'data-table', 'data-component']:
            if element.get(attr):
                selectors.append(f"{element.name}[{attr}='{element[attr]}']")
        
        # 标签选择器（最后选择）
        if not selectors:
            selectors.append(element.name)
        
        return selectors[0]
    
    def _is_label(self, element: Tag, text: str) -> bool:
        """判断文本是否为标签"""
        # 检查元素类名
        classes = element.get('class', [])
        label_classes = ['label', 'title', 'header', 'name', 'key']
        
        if any(label_class in ' '.join(classes).lower() for label_class in label_classes):
            return True
        
        # 检查文本特征
        if text.endswith(':') or text.endswith('：'):
            return True
        
        # 检查是否为纯数字或包含特殊符号（更可能是值）
        if re.match(r'^[\d\$¥€£%,.-]+$', text):
            return False
        
        return len(text) < 50  # 短文本更可能是标签
    
    def _calculate_table_confidence(self, headers: List[str], rows: List[List[str]]) -> float:
        """计算表格识别的置信度"""
        confidence = 0.5
        
        # 表头质量
        if headers and all(header.strip() for header in headers):
            confidence += 0.2
        
        # 数据一致性
        if rows:
            col_count = len(headers) if headers else len(rows[0])
            consistent_rows = sum(1 for row in rows if len(row) == col_count)
            consistency = consistent_rows / len(rows)
            confidence += consistency * 0.2
        
        # 数据类型识别
        if rows and headers:
            typed_columns = 0
            for col_idx in range(min(len(headers), len(rows[0]))):
                column_data = [row[col_idx] for row in rows if col_idx < len(row)]
                if self._detect_column_type(column_data):
                    typed_columns += 1
            
            if len(headers) > 0:
                type_ratio = typed_columns / len(headers)
                confidence += type_ratio * 0.1
        
        return min(confidence, 1.0)
    
    def _calculate_card_confidence(self, fields: List[str], values: List[str], count: int) -> float:
        """计算卡片识别的置信度"""
        confidence = 0.4
        
        # 卡片数量
        if count >= 3:
            confidence += 0.2
        
        # 字段质量
        if fields:
            confidence += 0.2
        
        # 值的多样性
        if values and len(set(values)) > 1:
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _calculate_confidence(self, tables: List[TableInfo], cards: List[CardInfo], lists: List[ElementInfo]) -> int:
        """计算整体分析置信度"""
        total_elements = len(tables) + len(cards) + len(lists)
        if total_elements == 0:
            return 0
        
        total_confidence = sum(t.confidence for t in tables) + \
                          sum(c.confidence for c in cards) + \
                          sum(l.confidence for l in lists)
        
        avg_confidence = total_confidence / total_elements
        return int(avg_confidence * 100)
    
    def _detect_column_type(self, column_data: List[str]) -> Optional[str]:
        """检测列的数据类型"""
        if not column_data:
            return None
        
        for data_type, pattern in self.data_patterns.items():
            matches = sum(1 for value in column_data if re.match(pattern, value.strip()))
            if matches / len(column_data) > 0.7:  # 70%匹配认为是该类型
                return data_type
        
        return None
    
    def _generate_suggested_rules(self, tables: List[TableInfo], cards: List[CardInfo], lists: List[ElementInfo]) -> Dict[str, Any]:
        """生成建议的提取规则"""
        rules = {}
        
        if tables:
            rules['tables'] = []
            for i, table in enumerate(tables):
                rules['tables'].append({
                    'selector': table.selector,
                    'name': f'table_data_{i+1}',
                    'columns': table.headers,
                    'type': 'table'
                })
        
        if cards:
            rules['cards'] = []
            for i, card in enumerate(cards):
                rules['cards'].append({
                    'selector': card.selector,
                    'name': f'card_data_{i+1}',
                    'fields': card.fields,
                    'type': 'card'
                })
        
        if lists:
            rules['lists'] = []
            for i, list_elem in enumerate(lists):
                rules['lists'].append({
                    'selector': list_elem.selector,
                    'name': f'list_data_{i+1}',
                    'type': 'list'
                })
        
        return rules
