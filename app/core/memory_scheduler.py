from typing import Dict, Any, Optional
import uuid
import json
from datetime import datetime
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from enum import Enum

from app.utils.logger import get_logger
from app.core.cache import cache_manager

logger = get_logger(__name__)

class JobStatus(Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    REVOKED = "REVOKED"

class MemoryTaskScheduler:
    """基于内存缓存的任务调度器"""
    
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.jobs = {}  # 存储任务信息
        self.lock = threading.RLock()
        self.max_workers = max_workers
        
    def _generate_job_id(self) -> str:
        """生成任务ID"""
        return f"job_{uuid.uuid4().hex[:8]}"
    
    def _store_job_info(self, job_id: str, job_info: Dict[str, Any]):
        """存储任务信息到缓存"""
        try:
            cache_key = f"job_status:{job_id}"
            cache_manager.set(cache_key, job_info, ttl=3600)  # 1小时过期
            
            with self.lock:
                self.jobs[job_id] = job_info
                
        except Exception as e:
            logger.error(f"Failed to store job info for {job_id}: {e}")
    
    def _get_job_info(self, job_id: str) -> Optional[Dict[str, Any]]:
        """从缓存获取任务信息"""
        try:
            # 先从内存获取
            with self.lock:
                if job_id in self.jobs:
                    return self.jobs[job_id]
            
            # 从缓存获取
            cache_key = f"job_status:{job_id}"
            job_info = cache_manager.get(cache_key)
            
            if job_info:
                with self.lock:
                    self.jobs[job_id] = job_info
                    
            return job_info
            
        except Exception as e:
            logger.error(f"Failed to get job info for {job_id}: {e}")
            return None
    
    def _update_job_status(self, job_id: str, status: JobStatus, result: Any = None, error: str = None, traceback: str = None):
        """更新任务状态"""
        job_info = self._get_job_info(job_id) or {}
        job_info.update({
            "job_id": job_id,
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat(),
            "result": result,
            "error": error,
            "traceback": traceback
        })
        self._store_job_info(job_id, job_info)
    
    def _execute_task(self, job_id: str, task_func, *args, **kwargs):
        """执行任务"""
        try:
            logger.info(f"Starting job {job_id}")
            self._update_job_status(job_id, JobStatus.RUNNING)
            
            # 执行任务
            result = task_func(*args, **kwargs)
            
            logger.info(f"Job {job_id} completed successfully")
            self._update_job_status(job_id, JobStatus.SUCCESS, result=result)
            
        except Exception as e:
            logger.error(f"Job {job_id} failed: {e}")
            import traceback as tb
            self._update_job_status(
                job_id, 
                JobStatus.FAILURE, 
                error=str(e),
                traceback=tb.format_exc()
            )
    
    def schedule_extraction_task(
        self,
        task_id: str,
        url: str,
        extraction_rules: Dict[str, Any],
        auth_config: Optional[Dict[str, Any]] = None,
        schedule_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """调度数据提取任务"""
        job_id = self._generate_job_id()
        
        # 创建任务信息
        job_info = {
            "job_id": job_id,
            "task_id": task_id,
            "task_type": "extraction",
            "url": url,
            "extraction_rules": extraction_rules,
            "auth_config": auth_config or {},
            "schedule_config": schedule_config or {},
            "status": JobStatus.PENDING.value,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        self._store_job_info(job_id, job_info)
        
        # 提交任务到线程池
        try:
            from app.core.tasks import extract_data_sync
            
            future = self.executor.submit(
                self._execute_task,
                job_id,
                extract_data_sync,
                task_id,
                url,
                extraction_rules,
                auth_config
            )
            
            logger.info(f"Scheduled extraction task {job_id} for task_id {task_id}")
            
        except Exception as e:
            logger.error(f"Failed to schedule extraction task: {e}")
            self._update_job_status(job_id, JobStatus.FAILURE, error=str(e))
        
        return job_id
    
    def schedule_page_analysis(
        self,
        url: str,
        analysis_config: Optional[Dict[str, Any]] = None
    ) -> str:
        """调度页面分析任务"""
        job_id = self._generate_job_id()
        
        job_info = {
            "job_id": job_id,
            "task_type": "analysis",
            "url": url,
            "analysis_config": analysis_config or {},
            "status": JobStatus.PENDING.value,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        self._store_job_info(job_id, job_info)
        
        try:
            from app.core.tasks import analyze_page_sync
            
            future = self.executor.submit(
                self._execute_task,
                job_id,
                analyze_page_sync,
                url,
                analysis_config
            )
            
            logger.info(f"Scheduled page analysis task {job_id} for URL {url}")
            
        except Exception as e:
            logger.error(f"Failed to schedule page analysis task: {e}")
            self._update_job_status(job_id, JobStatus.FAILURE, error=str(e))
        
        return job_id
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """获取作业状态"""
        try:
            job_info = self._get_job_info(job_id)
            
            if not job_info:
                return {
                    "job_id": job_id,
                    "status": "NOT_FOUND",
                    "error": "Job not found"
                }
            
            return {
                "job_id": job_id,
                "status": job_info.get("status", "UNKNOWN"),
                "result": job_info.get("result"),
                "error": job_info.get("error"),
                "traceback": job_info.get("traceback"),
                "created_at": job_info.get("created_at"),
                "updated_at": job_info.get("updated_at")
            }
            
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return {
                "job_id": job_id,
                "status": "ERROR",
                "error": str(e)
            }
    
    def cancel_job(self, job_id: str) -> bool:
        """取消作业"""
        try:
            job_info = self._get_job_info(job_id)
            
            if not job_info:
                logger.warning(f"Cannot cancel job {job_id}: job not found")
                return False
            
            current_status = job_info.get("status")
            
            if current_status in [JobStatus.SUCCESS.value, JobStatus.FAILURE.value, JobStatus.REVOKED.value]:
                logger.warning(f"Cannot cancel job {job_id}: job already completed with status {current_status}")
                return False
            
            # 更新状态为已取消
            self._update_job_status(job_id, JobStatus.REVOKED)
            
            logger.info(f"Job {job_id} cancelled successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        try:
            # 统计各种状态的任务数量
            status_counts = {
                "pending": 0,
                "running": 0,
                "success": 0,
                "failure": 0,
                "revoked": 0
            }
            
            with self.lock:
                for job_info in self.jobs.values():
                    status = job_info.get("status", "unknown").lower()
                    if status in status_counts:
                        status_counts[status] += 1
            
            return {
                "active_workers": self.max_workers,
                "total_jobs": len(self.jobs),
                "status_breakdown": status_counts,
                "queue_type": "memory",
                "healthy": True
            }
            
        except Exception as e:
            logger.error(f"Failed to get queue status: {e}")
            return {
                "active_workers": 0,
                "total_jobs": 0,
                "status_breakdown": {},
                "queue_type": "memory",
                "healthy": False,
                "error": str(e)
            }
    
    def cleanup_old_jobs(self, max_age_hours: int = 24):
        """清理旧任务"""
        try:
            current_time = datetime.utcnow()
            cleaned_count = 0
            
            with self.lock:
                jobs_to_remove = []
                
                for job_id, job_info in self.jobs.items():
                    created_at_str = job_info.get("created_at")
                    if created_at_str:
                        try:
                            created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
                            age_hours = (current_time - created_at).total_seconds() / 3600
                            
                            if age_hours > max_age_hours:
                                jobs_to_remove.append(job_id)
                                
                        except Exception as e:
                            logger.warning(f"Failed to parse created_at for job {job_id}: {e}")
                
                # 删除旧任务
                for job_id in jobs_to_remove:
                    self.jobs.pop(job_id, None)
                    cache_key = f"job_status:{job_id}"
                    cache_manager.delete(cache_key)
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old jobs")
                
        except Exception as e:
            logger.error(f"Failed to cleanup old jobs: {e}")

# 全局调度器实例
memory_scheduler = MemoryTaskScheduler()