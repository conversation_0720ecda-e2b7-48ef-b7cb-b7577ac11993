from fastapi import API<PERSON>outer, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.orm import Session
import json
import logging
from typing import Optional

from app.core.websocket_manager import manager, notifier
from app.api.v1.auth import get_current_user_from_token, get_current_user
from app.models.user import User
from app.database import get_db

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str, token: Optional[str] = None):
    """WebSocket连接端点"""
    
    # 验证用户身份
    if token:
        try:
            # 获取数据库会话
            from app.database import SessionLocal
            db = SessionLocal()
            try:
                user = await get_current_user_from_token(token, db)
                # 允许dashboard连接或匹配用户ID
                if not (user_id.startswith('dashboard_') or str(user.id) == user_id):
                    await websocket.close(code=1008, reason="Unauthorized")
                    return
            finally:
                db.close()
        except Exception as e:
            logger.warning(f"WebSocket authentication failed for user {user_id}: {e}")
            await websocket.close(code=1008, reason="Invalid token")
            return
    else:
        logger.warning(f"WebSocket connection without token for user {user_id}")
    
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_websocket_message(user_id, message)
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": "now"
                }, user_id)
            except Exception as e:
                logger.error(f"Error handling WebSocket message from {user_id}: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Internal server error",
                    "timestamp": "now"
                }, user_id)
                
    except WebSocketDisconnect:
        manager.disconnect(user_id)
        logger.info(f"WebSocket disconnected for user {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        manager.disconnect(user_id)

async def handle_websocket_message(user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get("type")
    
    if message_type == "subscribe_task":
        task_id = message.get("task_id")
        if task_id:
            manager.subscribe_to_task(user_id, task_id)
            await manager.send_personal_message({
                "type": "subscription_confirmed",
                "task_id": task_id,
                "message": f"已订阅任务 {task_id} 的状态更新"
            }, user_id)
        else:
            await manager.send_personal_message({
                "type": "error",
                "message": "Missing task_id for subscription"
            }, user_id)
    
    elif message_type == "unsubscribe_task":
        task_id = message.get("task_id")
        if task_id:
            manager.unsubscribe_from_task(user_id, task_id)
            await manager.send_personal_message({
                "type": "unsubscription_confirmed",
                "task_id": task_id,
                "message": f"已取消订阅任务 {task_id} 的状态更新"
            }, user_id)
        else:
            await manager.send_personal_message({
                "type": "error",
                "message": "Missing task_id for unsubscription"
            }, user_id)
    
    elif message_type == "ping":
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, user_id)
    
    elif message_type == "get_stats":
        stats = manager.get_connection_stats()
        await manager.send_personal_message({
            "type": "connection_stats",
            "stats": stats
        }, user_id)
    
    else:
        await manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, user_id)

@router.get("/ws/stats")
async def get_websocket_stats(current_user: User = Depends(get_current_user)):
    """获取WebSocket连接统计信息"""
    stats = manager.get_connection_stats()
    return {
        "status": "success",
        "data": stats
    }

@router.post("/ws/broadcast")
async def broadcast_message(
    message: dict,
    current_user: User = Depends(get_current_user)
):
    """管理员广播消息"""
    # 检查用户权限（这里简化处理，实际应该检查用户角色）
    if not current_user:
        raise HTTPException(status_code=403, detail="Permission denied")
    
    await manager.broadcast_to_all({
        "type": "admin_broadcast",
        "message": message.get("message", ""),
        "from": "system",
        "timestamp": "now"
    })
    
    return {
        "status": "success",
        "message": "Message broadcasted to all connected users"
    }

@router.post("/ws/notify/task/{task_id}")
async def notify_task_update(
    task_id: str,
    status: str,
    details: dict = None,
    current_user: User = Depends(get_current_user)
):
    """手动发送任务状态更新通知（用于测试）"""
    await notifier.notify_task_status_change(task_id, status, details)
    
    return {
        "status": "success",
        "message": f"Task {task_id} status notification sent"
    }