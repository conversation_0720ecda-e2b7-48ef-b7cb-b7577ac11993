"""
数据处理API端点
提供数据清洗、标准化、去重、质量评分等功能
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from uuid import UUID

from app.database import get_db
from app.api.v1.auth import get_current_user
from app.models.user import User
from app.models.result import ExtractionResult
from app.core.data_processor import DataProcessor, IncrementalUpdateManager
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

class ProcessingConfig(BaseModel):
    """数据处理配置"""
    cleaning: Dict[str, Any] = Field(default_factory=dict, description="清洗配置")
    standardize: bool = Field(True, description="是否标准化")
    remove_duplicates: bool = Field(True, description="是否去重")
    dedup_config: Dict[str, Any] = Field(default_factory=dict, description="去重配置")
    validate: bool = Field(True, description="是否验证")
    validation_rules: Dict[str, Any] = Field(default_factory=dict, description="验证规则")

class ProcessDataRequest(BaseModel):
    """处理数据请求"""
    data: List[Dict[str, Any]] = Field(..., description="要处理的数据")
    config: Optional[ProcessingConfig] = Field(None, description="处理配置")

class IncrementalUpdateRequest(BaseModel):
    """增量更新请求"""
    old_data: List[Dict[str, Any]] = Field(..., description="旧数据")
    new_data: List[Dict[str, Any]] = Field(..., description="新数据")
    strategy: str = Field("hash", description="更新策略: hash, timestamp, version")
    key_field: Optional[str] = Field(None, description="关键字段")

@router.post("/process")
async def process_data(
    request: ProcessDataRequest,
    current_user: User = Depends(get_current_user)
):
    """处理数据"""
    try:
        processor = DataProcessor()
        
        # 转换配置
        config = {}
        if request.config:
            config = {
                'cleaning': request.config.cleaning,
                'standardize': request.config.standardize,
                'remove_duplicates': request.config.remove_duplicates,
                'dedup_config': request.config.dedup_config,
                'validate': request.config.validate,
                'validation_rules': request.config.validation_rules
            }
        
        # 处理数据
        result = processor.process_data(request.data, config)
        
        return {
            "success": True,
            "data": {
                "original_count": result.original_count,
                "processed_count": result.processed_count,
                "removed_count": result.removed_count,
                "cleaned_data": result.cleaned_data,
                "quality_score": {
                    "overall_score": result.quality_score.overall_score,
                    "completeness": result.quality_score.completeness,
                    "accuracy": result.quality_score.accuracy,
                    "consistency": result.quality_score.consistency,
                    "uniqueness": result.quality_score.uniqueness,
                    "validity": result.quality_score.validity,
                    "details": result.quality_score.details
                },
                "processing_log": result.processing_log
            }
        }
        
    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"数据处理失败: {str(e)}"
        )

@router.post("/quality-score")
async def calculate_quality_score(
    request: ProcessDataRequest,
    current_user: User = Depends(get_current_user)
):
    """计算数据质量评分"""
    try:
        processor = DataProcessor()
        
        # 只计算质量评分，不进行实际处理
        quality_score = processor._calculate_quality_score(request.data, request.data)
        
        return {
            "success": True,
            "data": {
                "overall_score": quality_score.overall_score,
                "completeness": quality_score.completeness,
                "accuracy": quality_score.accuracy,
                "consistency": quality_score.consistency,
                "uniqueness": quality_score.uniqueness,
                "validity": quality_score.validity,
                "details": quality_score.details
            }
        }
        
    except Exception as e:
        logger.error(f"质量评分计算失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"质量评分计算失败: {str(e)}"
        )

@router.post("/incremental-update")
async def detect_incremental_changes(
    request: IncrementalUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """检测增量更新"""
    try:
        manager = IncrementalUpdateManager()
        
        changes = manager.detect_changes(
            old_data=request.old_data,
            new_data=request.new_data,
            strategy=request.strategy,
            key_field=request.key_field
        )
        
        return {
            "success": True,
            "data": {
                "added": changes['added'],
                "updated": changes['updated'],
                "deleted": changes['deleted'],
                "summary": {
                    "added_count": len(changes['added']),
                    "updated_count": len(changes['updated']),
                    "deleted_count": len(changes['deleted']),
                    "total_changes": len(changes['added']) + len(changes['updated']) + len(changes['deleted'])
                }
            }
        }
        
    except Exception as e:
        logger.error(f"增量更新检测失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"增量更新检测失败: {str(e)}"
        )

@router.post("/results/{result_id}/process")
async def process_extraction_result(
    result_id: UUID,
    config: Optional[ProcessingConfig] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """处理提取结果数据"""
    try:
        # 获取提取结果
        result = db.query(ExtractionResult).filter(
            ExtractionResult.id == result_id
        ).first()
        
        if not result:
            raise HTTPException(status_code=404, detail="提取结果不存在")
        
        # 检查权限（通过任务关联）
        from app.models.task import ExtractionTask
        task = db.query(ExtractionTask).filter(
            ExtractionTask.id == result.task_id,
            ExtractionTask.created_by == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(status_code=403, detail="无权限访问此结果")
        
        if not result.data:
            raise HTTPException(status_code=400, detail="结果数据为空")
        
        # 处理数据
        processor = DataProcessor()
        
        processing_config = {}
        if config:
            processing_config = {
                'cleaning': config.cleaning,
                'standardize': config.standardize,
                'remove_duplicates': config.remove_duplicates,
                'dedup_config': config.dedup_config,
                'validate': config.validate,
                'validation_rules': config.validation_rules
            }
        
        processing_result = processor.process_data(result.data, processing_config)
        
        # 更新结果数据
        result.data = processing_result.cleaned_data
        result.record_count = processing_result.processed_count
        
        # 添加处理信息到元数据
        if not result.metadata:
            result.metadata = {}
        
        result.metadata['processing'] = {
            'processed_at': str(datetime.utcnow()),
            'original_count': processing_result.original_count,
            'processed_count': processing_result.processed_count,
            'removed_count': processing_result.removed_count,
            'quality_score': {
                'overall_score': processing_result.quality_score.overall_score,
                'completeness': processing_result.quality_score.completeness,
                'accuracy': processing_result.quality_score.accuracy,
                'consistency': processing_result.quality_score.consistency,
                'uniqueness': processing_result.quality_score.uniqueness,
                'validity': processing_result.quality_score.validity
            },
            'processing_log': processing_result.processing_log
        }
        
        db.commit()
        
        return {
            "success": True,
            "message": "数据处理完成",
            "data": {
                "result_id": str(result.id),
                "original_count": processing_result.original_count,
                "processed_count": processing_result.processed_count,
                "removed_count": processing_result.removed_count,
                "quality_score": processing_result.quality_score.overall_score,
                "processing_log": processing_result.processing_log
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理提取结果失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"处理提取结果失败: {str(e)}"
        )

@router.get("/results/{result_id}/quality")
async def get_result_quality_score(
    result_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取结果数据质量评分"""
    try:
        # 获取提取结果
        result = db.query(ExtractionResult).filter(
            ExtractionResult.id == result_id
        ).first()
        
        if not result:
            raise HTTPException(status_code=404, detail="提取结果不存在")
        
        # 检查权限
        from app.models.task import ExtractionTask
        task = db.query(ExtractionTask).filter(
            ExtractionTask.id == result.task_id,
            ExtractionTask.created_by == current_user.id
        ).first()
        
        if not task:
            raise HTTPException(status_code=403, detail="无权限访问此结果")
        
        if not result.data:
            return {
                "success": True,
                "data": {
                    "overall_score": 0,
                    "message": "无数据可评分"
                }
            }
        
        # 计算质量评分
        processor = DataProcessor()
        quality_score = processor._calculate_quality_score(result.data, result.data)
        
        return {
            "success": True,
            "data": {
                "overall_score": quality_score.overall_score,
                "completeness": quality_score.completeness,
                "accuracy": quality_score.accuracy,
                "consistency": quality_score.consistency,
                "uniqueness": quality_score.uniqueness,
                "validity": quality_score.validity,
                "details": quality_score.details
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取质量评分失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取质量评分失败: {str(e)}"
        )
