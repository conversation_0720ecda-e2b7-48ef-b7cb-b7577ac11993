from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from uuid import uuid4
import hashlib
from datetime import datetime, timedelta

from app.database import get_db
from app.core.analyzer import PageAnalyzer
from app.core.extractor import create_extractor
from app.core.rule_recommender import RuleRecommender
from app.schemas import (
    AnalysisRequest, AnalysisResponse, ExtractionRequest, ExtractionResponse
)
from app.api.v1.auth import get_current_user
from app.models.user import User
from app.models.result import PageAnalysisCache
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_page_endpoint(
    request: AnalysisRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """分析页面结构"""
    try:
        url = str(request.url)
        url_hash = hashlib.sha256(url.encode()).hexdigest()
        
        # 检查缓存
        cached_analysis = db.query(PageAnalysisCache).filter(
            PageAnalysisCache.url_hash == url_hash
        ).first()
        
        if cached_analysis and cached_analysis.expires_at > datetime.utcnow():
            logger.info(f"Using cached analysis for {url}")
            analysis_result = cached_analysis.analysis_result
        else:
            # 使用真实的页面分析
            logger.info(f"Performing real-time analysis for {url}")
            async with create_extractor() as extractor:
                # 导航到页面
                await extractor._navigate_to_page(url, request.auth_config)
                
                # 获取页面HTML
                html_content = await extractor.page.content()
                
                # 分析页面结构
                analyzer = PageAnalyzer()
                result = analyzer.analyze_page(html_content, url)
                
                analysis_result = {
                    "tables": [
                        {
                            "selector": table.selector,
                            "headers": table.headers,
                            "row_count": table.row_count,
                            "confidence": table.confidence,
                            "sample_data": table.sample_data[:3]  # 只返回前3行样本
                        } for table in result.tables
                    ],
                    "cards": [
                        {
                            "selector": card.selector,
                            "fields": card.fields,
                            "sample_values": card.sample_values[:3],
                            "confidence": card.confidence
                        } for card in result.cards
                    ],
                    "lists": [
                        {
                            "selector": list_elem.selector,
                            "element_type": list_elem.element_type,
                            "confidence": list_elem.confidence
                        } for list_elem in result.lists
                    ]
                }
                
                # 缓存分析结果
                if cached_analysis:
                    cached_analysis.analysis_result = analysis_result
                    cached_analysis.confidence_score = result.confidence_score
                    cached_analysis.expires_at = datetime.utcnow() + timedelta(hours=24)
                else:
                    cached_analysis = PageAnalysisCache(
                        url_hash=url_hash,
                        url=url,
                        analysis_result=analysis_result,
                        confidence_score=result.confidence_score,
                        expires_at=datetime.utcnow() + timedelta(hours=24)
                    )
                    db.add(cached_analysis)
                
                db.commit()
        
        analysis_id = str(uuid4())
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            url=url,
            detected_elements=analysis_result,
            suggested_rules=result.suggested_rules if 'result' in locals() else {},
            confidence_score=cached_analysis.confidence_score
        )
        
    except Exception as e:
        logger.error(f"Page analysis failed for {request.url}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Page analysis failed: {str(e)}"
        )

@router.post("/execute", response_model=ExtractionResponse)
async def execute_extraction(
    request: ExtractionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """执行数据提取"""
    try:
        url = str(request.url)
        logger.info(f"Starting data extraction for {url}")
        
        # 使用真实的数据提取器
        async with create_extractor() as extractor:
            result = await extractor.extract_data(
                url=url,
                extraction_rules=request.extraction_rules,
                auth_config=request.auth_config,
                wait_config=request.options.get("wait_config") if request.options else None
            )
        
        extraction_id = uuid4()
        
        if result["status"] == "success":
            # 保存提取结果到数据库
            from app.models.result import ExtractionResult
            
            extraction_result = ExtractionResult(
                id=extraction_id,
                task_id=None,  # 直接执行的任务没有task_id
                data=result["data"],
                metadata=result["metadata"],
                record_count=result["metadata"].get("record_count", 0),
                extraction_duration=int(result["metadata"].get("duration", 0)),
                status="completed"
            )
            db.add(extraction_result)
            db.commit()
            
            return ExtractionResponse(
                extraction_id=extraction_id,
                status="completed",
                extracted_data=result["data"],
                metadata=result["metadata"],
                record_count=result["metadata"].get("record_count", 0),
                extraction_duration=int(result["metadata"].get("duration", 0))
            )
        else:
            # 提取失败
            extraction_result = ExtractionResult(
                id=extraction_id,
                task_id=None,
                data={},
                metadata=result.get("metadata", {}),
                record_count=0,
                status="failed",
                error_message=result.get("error", "Unknown error")
            )
            db.add(extraction_result)
            db.commit()
            
            raise HTTPException(
                status_code=500,
                detail=f"Data extraction failed: {result.get('error', 'Unknown error')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Data extraction failed for {request.url}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Data extraction failed: {str(e)}"
        )


@router.post("/recommend-rules")
async def recommend_extraction_rules(
    request: AnalysisRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """基于页面分析推荐提取规则"""
    try:
        url = str(request.url)
        url_hash = hashlib.sha256(url.encode()).hexdigest()

        # 首先获取页面分析结果
        cached_analysis = db.query(PageAnalysisCache).filter(
            PageAnalysisCache.url_hash == url_hash
        ).first()

        analysis_result = None

        if cached_analysis and cached_analysis.expires_at > datetime.utcnow():
            logger.info(f"Using cached analysis for rule recommendation: {url}")
            analysis_result = cached_analysis.analysis_result
        else:
            # 执行页面分析
            logger.info(f"Performing analysis for rule recommendation: {url}")
            async with create_extractor() as extractor:
                await extractor._navigate_to_page(url, request.auth_config)
                html_content = await extractor.page.content()

                analyzer = PageAnalyzer()
                result = analyzer.analyze_page(html_content, url)

                analysis_result = {
                    "url": result.url,
                    "tables": [
                        {
                            "selector": table.selector,
                            "headers": table.headers,
                            "row_count": table.row_count,
                            "confidence": table.confidence,
                            "sample_data": table.sample_data
                        }
                        for table in result.tables
                    ],
                    "cards": [
                        {
                            "selector": card.selector,
                            "fields": card.fields,
                            "confidence": card.confidence,
                            "sample_values": card.sample_values
                        }
                        for card in result.cards
                    ],
                    "lists": [
                        {
                            "selector": list_elem.selector,
                            "element_type": list_elem.element_type,
                            "confidence": list_elem.confidence
                        }
                        for list_elem in result.lists
                    ],
                    "suggested_rules": result.suggested_rules,
                    "confidence_score": result.confidence_score
                }

                # 缓存分析结果
                if cached_analysis:
                    cached_analysis.analysis_result = analysis_result
                    cached_analysis.confidence_score = result.confidence_score
                    cached_analysis.expires_at = datetime.utcnow() + timedelta(hours=24)
                else:
                    cached_analysis = PageAnalysisCache(
                        url_hash=url_hash,
                        url=url,
                        analysis_result=analysis_result,
                        confidence_score=result.confidence_score,
                        expires_at=datetime.utcnow() + timedelta(hours=24)
                    )
                    db.add(cached_analysis)

                db.commit()

        # 使用规则推荐器生成推荐
        recommender = RuleRecommender()

        # 重构分析结果为推荐器需要的格式
        from app.core.analyzer import AnalysisResult, TableInfo, CardInfo, ElementInfo

        tables = []
        for table_data in analysis_result.get("tables", []):
            table_info = TableInfo(
                selector=table_data["selector"],
                element_type="table",
                confidence=table_data["confidence"],
                attributes={},
                headers=table_data["headers"],
                row_count=table_data["row_count"],
                sample_data=table_data.get("sample_data", [])
            )
            tables.append(table_info)

        cards = []
        for card_data in analysis_result.get("cards", []):
            card_info = CardInfo(
                selector=card_data["selector"],
                element_type="card",
                confidence=card_data["confidence"],
                attributes={},
                fields=card_data["fields"],
                sample_values=card_data.get("sample_values", [])
            )
            cards.append(card_info)

        lists = []
        for list_data in analysis_result.get("lists", []):
            list_info = ElementInfo(
                selector=list_data["selector"],
                element_type=list_data["element_type"],
                confidence=list_data["confidence"],
                attributes={}
            )
            lists.append(list_info)

        reconstructed_result = AnalysisResult(
            url=analysis_result["url"],
            tables=tables,
            cards=cards,
            lists=lists,
            suggested_rules=analysis_result.get("suggested_rules", {}),
            confidence_score=analysis_result["confidence_score"]
        )

        # 生成推荐
        recommendation_result = recommender.recommend_rules(reconstructed_result)

        return {
            "success": True,
            "url": url,
            "recommendations": [
                {
                    "rule_type": rec.rule_type,
                    "selector": rec.selector,
                    "name": rec.name,
                    "confidence": rec.confidence,
                    "description": rec.description,
                    "config": rec.config,
                    "priority": rec.priority
                }
                for rec in recommendation_result.recommendations
            ],
            "total_confidence": recommendation_result.total_confidence,
            "suggested_config": recommendation_result.suggested_config,
            "warnings": recommendation_result.warnings,
            "analysis_confidence": analysis_result["confidence_score"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Rule recommendation failed for {request.url}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Rule recommendation failed: {str(e)}"
        )
