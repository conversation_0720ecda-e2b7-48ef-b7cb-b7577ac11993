from fastapi import APIRouter, Response, Depends
from fastapi.responses import PlainTextResponse
from typing import Dict, Any

from app.utils.metrics import metrics_collector, performance_monitor, CONTENT_TYPE_LATEST
from app.api.v1.auth import get_current_user
from app.models.user import User
from app.utils.performance import (
    performance_analyzer,
    query_optimizer,
    cache_optimizer,
    memory_optimizer,
    connection_pool_optimizer
)

router = APIRouter()

@router.get("/metrics")
async def get_metrics():
    """获取Prometheus格式的指标"""
    # 更新系统指标
    metrics_collector.update_system_metrics()

    # 获取指标数据
    metrics_data = metrics_collector.get_metrics()

    return PlainTextResponse(
        content=metrics_data,
        media_type=CONTENT_TYPE_LATEST
    )

@router.get("/performance")
async def get_performance_stats(current_user: User = Depends(get_current_user)):
    """获取性能统计信息"""
    return {
        "request_stats": performance_monitor.get_request_stats(),
        "system_info": performance_monitor.get_system_info()
    }

@router.get("/performance/report")
async def get_performance_report(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """获取完整性能报告"""
    return performance_analyzer.generate_performance_report()

@router.get("/performance/queries")
async def get_query_performance(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """获取查询性能分析"""
    return performance_analyzer.analyze_query_performance()

@router.get("/performance/cache")
async def get_cache_performance(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """获取缓存性能分析"""
    return performance_analyzer.analyze_cache_performance()

@router.get("/performance/memory")
async def get_memory_performance(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """获取内存性能分析"""
    return performance_analyzer.analyze_memory_performance()

@router.get("/performance/database")
async def get_database_performance(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """获取数据库性能统计"""
    return {
        "connection_pool": connection_pool_optimizer.get_pool_stats(),
        "slow_queries": query_optimizer.get_slow_queries(),
        "query_stats": query_optimizer.query_stats
    }

@router.post("/performance/optimize")
async def optimize_performance(current_user: User = Depends(get_current_user)) -> Dict[str, str]:
    """执行性能优化"""
    try:
        # 这里可以添加自动优化逻辑
        # 例如：清理缓存、优化数据库索引等

        return {
            "status": "success",
            "message": "Performance optimization completed"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Optimization failed: {str(e)}"
        }
