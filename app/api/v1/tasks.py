from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from app.database import get_db
from app.models.task import ExtractionTask
from app.schemas import (
    TaskCreateRequest, TaskUpdateRequest, TaskResponse, 
    TaskListResponse, PaginationResponse, MessageResponse
)
from app.api.v1.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新的提取任务"""
    task = ExtractionTask.create(
        db_session=db,
        name=task_data.name,
        url=str(task_data.url),
        extraction_rules=task_data.extraction_rules,
        schedule_config=task_data.schedule_config,
        auth_config=task_data.auth_config,
        created_by=current_user.id
    )
    
    return TaskResponse.from_orm(task)

@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务列表"""
    query = db.query(ExtractionTask)
    
    # 过滤条件
    if status:
        query = query.filter(ExtractionTask.status == status)
    
    if search:
        query = query.filter(ExtractionTask.name.ilike(f"%{search}%"))
    
    # 分页
    total = query.count()
    offset = (page - 1) * limit
    tasks = query.offset(offset).limit(limit).all()
    
    # 计算分页信息
    pages = (total + limit - 1) // limit
    
    return TaskListResponse(
        tasks=[TaskResponse.from_orm(task) for task in tasks],
        pagination=PaginationResponse(
            page=page,
            limit=limit,
            total=total,
            pages=pages
        )
    )

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务详情"""
    task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return TaskResponse.from_orm(task)

@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: UUID,
    task_data: TaskUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新任务"""
    task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 更新字段
    update_data = task_data.dict(exclude_unset=True)
    if 'url' in update_data:
        update_data['url'] = str(update_data['url'])
    
    task.update(db, **update_data)
    
    return TaskResponse.from_orm(task)

@router.delete("/{task_id}", response_model=MessageResponse)
async def delete_task(
    task_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除任务"""
    task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    db.delete(task)
    db.commit()
    
    return MessageResponse(
        message="Task deleted successfully",
        details={"task_id": str(task_id)}
    )

@router.post("/{task_id}/execute")
async def execute_task(
    task_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """立即执行任务"""
    task = db.query(ExtractionTask).filter(ExtractionTask.id == task_id).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 集成Celery任务队列
    from app.core.tasks import extract_data
    from app.core.websocket_manager import notifier
    
    task_data = {
        "task_id": str(task.id),
        "url": task.url,
        "extraction_rules": task.extraction_rules,
        "auth_config": task.auth_config
    }
    
    # 提交到Celery队列
    job = extract_data.delay(task_data)
    
    # 更新任务状态
    task.status = "queued"
    db.commit()
    
    # 发送WebSocket通知
    await notifier.notify_task_status_change(
        str(task_id), 
        "queued", 
        {
            "job_id": job.id,
            "message": "任务已加入执行队列"
        }
    )
    
    return {
        "job_id": job.id,
        "task_id": str(task_id),
        "status": "queued",
        "message": "Task execution queued successfully"
    }
