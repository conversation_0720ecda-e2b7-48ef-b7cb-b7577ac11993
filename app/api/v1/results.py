from fastapi import APIRouter, HTTPException, Depends, Query, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import json
import csv
import io
from datetime import datetime

from app.database import get_db
from app.models.result import ExtractionResult
from app.models.task import ExtractionTask
from app.schemas import (
    ResultResponse, ResultListResponse, PaginationResponse, MessageResponse
)
from app.api.v1.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("/", response_model=ResultListResponse)
async def get_results(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    task_id: Optional[UUID] = Query(None),
    success_only: Optional[bool] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提取结果列表"""
    # 构建查询，只返回用户自己的结果
    query = db.query(ExtractionResult).join(ExtractionTask).filter(
        ExtractionTask.created_by == current_user.id
    )

    # 过滤条件
    if task_id:
        query = query.filter(ExtractionResult.task_id == task_id)

    if success_only is not None:
        query = query.filter(ExtractionResult.success == success_only)

    # 按提取时间倒序排列
    query = query.order_by(ExtractionResult.extracted_at.desc())

    # 分页
    total = query.count()
    offset = (page - 1) * limit
    results = query.offset(offset).limit(limit).all()

    # 计算分页信息
    pages = (total + limit - 1) // limit

    return ResultListResponse(
        results=[ResultResponse.model_validate(result) for result in results],
        pagination=PaginationResponse(
            page=page,
            limit=limit,
            total=total,
            pages=pages
        )
    )

@router.get("/{result_id}", response_model=ResultResponse)
async def get_result(
    result_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提取结果详情"""
    result = db.query(ExtractionResult).join(ExtractionTask).filter(
        ExtractionResult.id == result_id,
        ExtractionTask.created_by == current_user.id
    ).first()

    if not result:
        raise HTTPException(status_code=404, detail="Result not found")

    return ResultResponse.model_validate(result)

@router.get("/{result_id}/download")
async def download_result(
    result_id: UUID,
    format: str = Query("json", pattern="^(json|csv|xlsx)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """下载提取结果"""
    result = db.query(ExtractionResult).join(ExtractionTask).filter(
        ExtractionResult.id == result_id,
        ExtractionTask.created_by == current_user.id
    ).first()

    if not result:
        raise HTTPException(status_code=404, detail="Result not found")

    if not result.data:
        raise HTTPException(status_code=400, detail="No data to export")

    # 获取任务信息用于文件名
    task = db.query(ExtractionTask).filter(ExtractionTask.id == result.task_id).first()
    task_name = task.name if task else "extraction"
    timestamp = result.extracted_at.strftime("%Y%m%d_%H%M%S")

    if format == "json":
        content = json.dumps(result.data, ensure_ascii=False, indent=2)
        filename = f"{task_name}_{timestamp}.json"

        return Response(
            content=content.encode('utf-8'),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    elif format == "csv":
        output = io.StringIO()

        # 处理嵌套数据结构
        data = result.data
        if isinstance(data, dict):
            # 如果是字典，尝试找到列表数据
            for key, value in data.items():
                if isinstance(value, list) and value:
                    data = value
                    break

        if isinstance(data, list) and data:
            # 获取所有字段名
            fieldnames = set()
            for item in data:
                if isinstance(item, dict):
                    fieldnames.update(item.keys())

            fieldnames = sorted(list(fieldnames))

            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()

            for item in data:
                if isinstance(item, dict):
                    # 处理嵌套字典，将其转换为字符串
                    row = {}
                    for field in fieldnames:
                        value = item.get(field, "")
                        if isinstance(value, (dict, list)):
                            value = json.dumps(value, ensure_ascii=False)
                        row[field] = value
                    writer.writerow(row)

        content = output.getvalue()
        filename = f"{task_name}_{timestamp}.csv"

        return Response(
            content=content.encode('utf-8-sig'),  # 使用BOM以支持Excel
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    elif format == "xlsx":
        raise HTTPException(status_code=501, detail="Excel export requires additional packages")

    raise HTTPException(status_code=400, detail="Invalid format")

@router.delete("/{result_id}", response_model=MessageResponse)
async def delete_result(
    result_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除提取结果"""
    result = db.query(ExtractionResult).join(ExtractionTask).filter(
        ExtractionResult.id == result_id,
        ExtractionTask.created_by == current_user.id
    ).first()

    if not result:
        raise HTTPException(status_code=404, detail="Result not found")

    db.delete(result)
    db.commit()

    return MessageResponse(
        message="Result deleted successfully",
        details={"result_id": str(result_id)}
    )

@router.get("/task/{task_id}/latest", response_model=ResultResponse)
async def get_latest_result_for_task(
    task_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务的最新提取结果"""
    # 验证任务存在且属于当前用户
    task = db.query(ExtractionTask).filter(
        ExtractionTask.id == task_id,
        ExtractionTask.created_by == current_user.id
    ).first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取最新的提取结果
    result = db.query(ExtractionResult).filter(
        ExtractionResult.task_id == task_id
    ).order_by(ExtractionResult.extracted_at.desc()).first()

    if not result:
        raise HTTPException(status_code=404, detail="No results found for this task")

    return ResultResponse.model_validate(result)

@router.get("/stats/summary")
async def get_results_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提取结果统计摘要"""
    # 获取用户的所有结果
    results_query = db.query(ExtractionResult).join(ExtractionTask).filter(
        ExtractionTask.created_by == current_user.id
    )

    total_results = results_query.count()
    successful_results = results_query.filter(ExtractionResult.success == True).count()
    failed_results = total_results - successful_results

    # 计算总记录数
    total_records = db.query(
        db.func.sum(ExtractionResult.record_count)
    ).join(ExtractionTask).filter(
        ExtractionTask.created_by == current_user.id,
        ExtractionResult.success == True
    ).scalar() or 0

    # 最近的结果
    recent_result = results_query.order_by(
        ExtractionResult.extracted_at.desc()
    ).first()

    return {
        "total_results": total_results,
        "successful_results": successful_results,
        "failed_results": failed_results,
        "success_rate": (successful_results / total_results * 100) if total_results > 0 else 0,
        "total_records_extracted": total_records,
        "last_extraction": recent_result.extracted_at.isoformat() if recent_result else None,
        "timestamp": datetime.now().isoformat()
    }
