from fastapi import APIRouter, HTTPException
from datetime import datetime
import asyncio

router = APIRouter()

@router.get("/health")
async def health_check():
    """系统健康检查"""
    checks = {
        "api": True,
        "database": await check_database(),
        "timestamp": datetime.utcnow().isoformat()
    }
    
    all_healthy = all(isinstance(v, bool) and v for k, v in checks.items() if k != "timestamp")
    status_code = 200 if all_healthy else 503
    
    return {
        "status": "healthy" if all_healthy else "unhealthy",
        "checks": checks
    }

async def check_database():
    """检查数据库连接"""
    try:
        from app.database import SessionLocal
        from sqlalchemy import text
        db = SessionLocal()
        try:
            # 使用text()包装SQL语句以确保兼容性
            result = db.execute(text("SELECT 1"))
            result.fetchone()  # 确保查询执行成功
            return True
        finally:
            db.close()
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False
