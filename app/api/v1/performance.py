from fastapi import APIRouter, Depends, Query
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import psutil
import time

# from app.utils.performance import get_system_metrics, get_performance_stats
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

@router.get("/performance/system")
async def get_system_performance():
    """获取系统性能指标"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "cpu": {
                "usage_percent": cpu_percent,
                "count": psutil.cpu_count(),
                "count_logical": psutil.cpu_count(logical=True)
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "usage_percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "usage_percent": (disk.used / disk.total) * 100
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
        }
    except Exception as e:
        logger.error(f"Failed to get system performance: {e}")
        return {
            "error": "Failed to retrieve system performance metrics",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/performance/app")
async def get_app_performance():
    """获取应用性能指标"""
    try:
        import os
        process = psutil.Process(os.getpid())
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "process": {
                "pid": process.pid,
                "cpu_percent": process.cpu_percent(),
                "memory_info": process.memory_info()._asdict(),
                "memory_percent": process.memory_percent(),
                "num_threads": process.num_threads(),
                "create_time": process.create_time(),
                "status": process.status()
            }
        }
    except Exception as e:
        logger.error(f"Failed to get app performance: {e}")
        return {
            "error": "Failed to retrieve app performance metrics",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/performance/database")
async def get_database_performance():
    """获取数据库性能指标"""
    try:
        from app.database import SessionLocal
        from app.models.task import ExtractionTask
        from app.models.result import ExtractionResult
        
        db = SessionLocal()
        start_time = time.time()
        
        try:
            # 简单查询测试
            task_count = db.query(ExtractionTask).count()
            result_count = db.query(ExtractionResult).count()
            
            query_time = time.time() - start_time
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "database": {
                    "query_time_ms": round(query_time * 1000, 2),
                    "task_count": task_count,
                    "result_count": result_count,
                    "status": "healthy" if query_time < 1.0 else "slow"
                }
            }
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get database performance: {e}")
        return {
            "error": "Failed to retrieve database performance metrics",
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/performance/summary")
async def get_performance_summary():
    """获取性能摘要"""
    try:
        # 获取各项性能指标
        system_perf = await get_system_performance()
        app_perf = await get_app_performance()
        db_perf = await get_database_performance()
        
        # 计算整体健康状态
        health_score = 100
        issues = []
        
        if system_perf.get("cpu", {}).get("usage_percent", 0) > 80:
            health_score -= 20
            issues.append("High CPU usage")
            
        if system_perf.get("memory", {}).get("usage_percent", 0) > 80:
            health_score -= 20
            issues.append("High memory usage")
            
        if system_perf.get("disk", {}).get("usage_percent", 0) > 90:
            health_score -= 15
            issues.append("Low disk space")
            
        if db_perf.get("database", {}).get("query_time_ms", 0) > 1000:
            health_score -= 25
            issues.append("Slow database queries")
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "health_score": max(0, health_score),
            "status": "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical",
            "issues": issues,
            "system": system_perf,
            "application": app_perf,
            "database": db_perf
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance summary: {e}")
        return {
            "error": "Failed to retrieve performance summary",
            "timestamp": datetime.utcnow().isoformat()
        }
