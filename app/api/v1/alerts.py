"""告警管理API"""
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.core.alert_manager import alert_manager, AlertRule, AlertLevel
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

class AlertRuleCreate(BaseModel):
    """创建告警规则请求"""
    name: str
    description: str
    metric: str
    threshold: float
    operator: str
    level: str
    duration: int = 60
    enabled: bool = True

class AlertRuleUpdate(BaseModel):
    """更新告警规则请求"""
    description: str = None
    threshold: float = None
    operator: str = None
    level: str = None
    duration: int = None
    enabled: bool = None

class AlertRuleResponse(BaseModel):
    """告警规则响应"""
    name: str
    description: str
    metric: str
    threshold: float
    operator: str
    level: str
    duration: int
    enabled: bool

@router.get("/rules", response_model=List[AlertRuleResponse])
async def get_alert_rules():
    """获取所有告警规则"""
    try:
        rules = alert_manager.get_rules()
        return [
            AlertRuleResponse(
                name=rule.name,
                description=rule.description,
                metric=rule.metric,
                threshold=rule.threshold,
                operator=rule.operator,
                level=rule.level.value,
                duration=rule.duration,
                enabled=rule.enabled
            )
            for rule in rules
        ]
    except Exception as e:
        logger.error(f"Error getting alert rules: {e}")
        raise HTTPException(status_code=500, detail="获取告警规则失败")

@router.post("/rules", response_model=AlertRuleResponse)
async def create_alert_rule(rule_data: AlertRuleCreate):
    """创建告警规则"""
    try:
        # 验证告警级别
        try:
            level = AlertLevel(rule_data.level)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的告警级别")
        
        # 验证操作符
        valid_operators = ['>', '<', '>=', '<=', '==', '!=']
        if rule_data.operator not in valid_operators:
            raise HTTPException(status_code=400, detail="无效的操作符")
        
        # 检查规则名称是否已存在
        existing_rules = alert_manager.get_rules()
        if any(rule.name == rule_data.name for rule in existing_rules):
            raise HTTPException(status_code=400, detail="告警规则名称已存在")
        
        # 创建规则
        rule = AlertRule(
            name=rule_data.name,
            description=rule_data.description,
            metric=rule_data.metric,
            threshold=rule_data.threshold,
            operator=rule_data.operator,
            level=level,
            duration=rule_data.duration,
            enabled=rule_data.enabled
        )
        
        alert_manager.add_rule(rule)
        
        return AlertRuleResponse(
            name=rule.name,
            description=rule.description,
            metric=rule.metric,
            threshold=rule.threshold,
            operator=rule.operator,
            level=rule.level.value,
            duration=rule.duration,
            enabled=rule.enabled
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating alert rule: {e}")
        raise HTTPException(status_code=500, detail="创建告警规则失败")

@router.put("/rules/{rule_name}", response_model=AlertRuleResponse)
async def update_alert_rule(rule_name: str, rule_data: AlertRuleUpdate):
    """更新告警规则"""
    try:
        rules = alert_manager.get_rules()
        rule = next((r for r in rules if r.name == rule_name), None)
        
        if not rule:
            raise HTTPException(status_code=404, detail="告警规则不存在")
        
        # 更新规则属性
        if rule_data.description is not None:
            rule.description = rule_data.description
        if rule_data.threshold is not None:
            rule.threshold = rule_data.threshold
        if rule_data.operator is not None:
            valid_operators = ['>', '<', '>=', '<=', '==', '!=']
            if rule_data.operator not in valid_operators:
                raise HTTPException(status_code=400, detail="无效的操作符")
            rule.operator = rule_data.operator
        if rule_data.level is not None:
            try:
                rule.level = AlertLevel(rule_data.level)
            except ValueError:
                raise HTTPException(status_code=400, detail="无效的告警级别")
        if rule_data.duration is not None:
            rule.duration = rule_data.duration
        if rule_data.enabled is not None:
            rule.enabled = rule_data.enabled
        
        return AlertRuleResponse(
            name=rule.name,
            description=rule.description,
            metric=rule.metric,
            threshold=rule.threshold,
            operator=rule.operator,
            level=rule.level.value,
            duration=rule.duration,
            enabled=rule.enabled
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating alert rule: {e}")
        raise HTTPException(status_code=500, detail="更新告警规则失败")

@router.delete("/rules/{rule_name}")
async def delete_alert_rule(rule_name: str):
    """删除告警规则"""
    try:
        rules = alert_manager.get_rules()
        rule = next((r for r in rules if r.name == rule_name), None)
        
        if not rule:
            raise HTTPException(status_code=404, detail="告警规则不存在")
        
        alert_manager.remove_rule(rule_name)
        
        return {"message": "告警规则删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting alert rule: {e}")
        raise HTTPException(status_code=500, detail="删除告警规则失败")

@router.post("/rules/{rule_name}/enable")
async def enable_alert_rule(rule_name: str):
    """启用告警规则"""
    try:
        rules = alert_manager.get_rules()
        rule = next((r for r in rules if r.name == rule_name), None)
        
        if not rule:
            raise HTTPException(status_code=404, detail="告警规则不存在")
        
        alert_manager.enable_rule(rule_name)
        
        return {"message": "告警规则启用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error enabling alert rule: {e}")
        raise HTTPException(status_code=500, detail="启用告警规则失败")

@router.post("/rules/{rule_name}/disable")
async def disable_alert_rule(rule_name: str):
    """禁用告警规则"""
    try:
        rules = alert_manager.get_rules()
        rule = next((r for r in rules if r.name == rule_name), None)
        
        if not rule:
            raise HTTPException(status_code=404, detail="告警规则不存在")
        
        alert_manager.disable_rule(rule_name)
        
        return {"message": "告警规则禁用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling alert rule: {e}")
        raise HTTPException(status_code=500, detail="禁用告警规则失败")

@router.get("/active")
async def get_active_alerts():
    """获取活跃告警"""
    try:
        return {
            "alerts": alert_manager.get_active_alerts(),
            "count": len(alert_manager.get_active_alerts())
        }
    except Exception as e:
        logger.error(f"Error getting active alerts: {e}")
        raise HTTPException(status_code=500, detail="获取活跃告警失败")

@router.get("/history")
async def get_alert_history(
    limit: int = Query(100, ge=1, le=1000, description="返回记录数量")
):
    """获取告警历史"""
    try:
        history = alert_manager.get_alert_history(limit)
        return {
            "alerts": history,
            "count": len(history),
            "total": len(alert_manager.alert_history)
        }
    except Exception as e:
        logger.error(f"Error getting alert history: {e}")
        raise HTTPException(status_code=500, detail="获取告警历史失败")

@router.delete("/history")
async def clear_alert_history():
    """清除告警历史"""
    try:
        alert_manager.clear_alert_history()
        return {"message": "告警历史清除成功"}
    except Exception as e:
        logger.error(f"Error clearing alert history: {e}")
        raise HTTPException(status_code=500, detail="清除告警历史失败")

@router.get("/metrics")
async def get_alert_metrics():
    """获取告警相关指标"""
    try:
        rules = alert_manager.get_rules()
        active_alerts = alert_manager.get_active_alerts()
        history = alert_manager.get_alert_history()
        
        # 统计各级别告警数量
        level_counts = {}
        for alert in active_alerts:
            level = alert['level']
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # 统计最近24小时告警数量
        from datetime import datetime, timedelta
        now = datetime.now()
        recent_alerts = [
            alert for alert in history
            if datetime.fromisoformat(alert['timestamp']) > now - timedelta(hours=24)
        ]
        
        return {
            "total_rules": len(rules),
            "enabled_rules": len([r for r in rules if r.enabled]),
            "active_alerts": len(active_alerts),
            "alert_levels": level_counts,
            "recent_24h_alerts": len(recent_alerts),
            "total_history": len(history)
        }
        
    except Exception as e:
        logger.error(f"Error getting alert metrics: {e}")
        raise HTTPException(status_code=500, detail="获取告警指标失败")