from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any

# 根据环境变量选择调度器
import os
USE_MEMORY_SCHEDULER = os.getenv("USE_MEMORY_SCHEDULER", "true").lower() == "true"

if USE_MEMORY_SCHEDULER:
    from app.core.memory_scheduler import memory_scheduler as scheduler
else:
    from app.core.scheduler import scheduler
from app.schemas import JobStatusResponse, MessageResponse
from app.api.v1.auth import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取作业状态"""
    try:
        status_info = scheduler.get_job_status(job_id)
        
        # Convert datetime strings to datetime objects if present
        from datetime import datetime
        created_at = None
        updated_at = None
        
        if status_info.get("created_at"):
            try:
                created_at = datetime.fromisoformat(status_info["created_at"].replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                pass
                
        if status_info.get("updated_at"):
            try:
                updated_at = datetime.fromisoformat(status_info["updated_at"].replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                pass
        
        return JobStatusResponse(
            job_id=job_id,
            status=status_info.get("status", "UNKNOWN"),
            result=status_info.get("result"),
            error=status_info.get("error"),
            traceback=status_info.get("traceback"),
            created_at=created_at,
            updated_at=updated_at
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )

@router.delete("/{job_id}", response_model=MessageResponse)
async def cancel_job(
    job_id: str,
    current_user: User = Depends(get_current_user)
):
    """取消作业"""
    try:
        success = scheduler.cancel_job(job_id)
        
        if success:
            return MessageResponse(
                message="Job cancelled successfully",
                details={"job_id": job_id}
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="Failed to cancel job"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel job: {str(e)}"
        )

@router.get("/queue/status")
async def get_queue_status(
    current_user: User = Depends(get_current_user)
):
    """获取队列状态"""
    try:
        status = scheduler.get_queue_status()
        return status
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get queue status: {str(e)}"
        )
