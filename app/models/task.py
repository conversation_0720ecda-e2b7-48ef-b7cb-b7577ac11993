from sqlalchemy import Column, String, DateTime, Text, JSON, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy import TypeDecorator
from app.database import Base
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import os

# UUID type that works with both SQLite and PostgreSQL
class GUID(TypeDecorator):
    impl = String
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID(as_uuid=True))
        else:
            return dialect.type_descriptor(String(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return str(value)
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                return uuid.UUID(value)
            return value

class ExtractionTask(Base):
    __tablename__ = "extraction_tasks"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, index=True)
    url = Column(Text, nullable=False)
    extraction_rules = Column(JSON, nullable=False)
    schedule_config = Column(JSON)
    auth_config = Column(JSON)
    status = Column(String(50), default='active', index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(GUID())
    
    # 新增字段 - 修复tasks.py中引用的缺失字段
    last_run = Column(DateTime(timezone=True))
    last_success = Column(DateTime(timezone=True))
    total_runs = Column(Integer, default=0)
    next_run = Column(DateTime(timezone=True))
    error_message = Column(Text)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': str(self.id),
            'name': self.name,
            'url': self.url,
            'extraction_rules': self.extraction_rules,
            'schedule_config': self.schedule_config,
            'auth_config': self.auth_config,
            'status': self.status,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': str(self.created_by) if self.created_by else None,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'last_success': self.last_success.isoformat() if self.last_success else None,
            'total_runs': self.total_runs,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'error_message': self.error_message
        }
    
    @classmethod
    def create(cls, db_session, **kwargs):
        task = cls(**kwargs)
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        return task
    
    def update(self, db_session, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now(datetime.timezone.utc)
        db_session.commit()
        db_session.refresh(self)
        return self
