from sqlalchemy import Column, String, DateTime, Text, JSO<PERSON>, Integer, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
from app.models.task import GUID  # Import our custom GUID type
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

class ExtractionResult(Base):
    __tablename__ = "extraction_results"

    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    task_id = Column(GUID(), ForeignKey('extraction_tasks.id'), nullable=True, index=True)
    job_id = Column(String(255), index=True)
    data = Column(JSON, nullable=False)
    meta_data = Column(JSON)
    data_hash = Column(String(64), index=True)  # For duplicate detection
    record_count = Column(Integer, default=0)
    extraction_duration = Column(Integer)  # Duration in seconds
    status = Column(String(50), default='completed', index=True)
    error_message = Column(Text)
    extracted_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # Relationship
    task = relationship("ExtractionTask", backref="results")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': str(self.id),
            'task_id': str(self.task_id),
            'job_id': self.job_id,
            'data': self.data,
            'metadata': self.meta_data,
            'data_hash': self.data_hash,
            'record_count': self.record_count,
            'extraction_duration': self.extraction_duration,
            'status': self.status,
            'error_message': self.error_message,
            'extracted_at': self.extracted_at.isoformat() if self.extracted_at else None
        }
    
    @classmethod
    def create(cls, db_session, **kwargs):
        result = cls(**kwargs)
        db_session.add(result)
        db_session.commit()
        db_session.refresh(result)
        return result

class PageAnalysisCache(Base):
    __tablename__ = "page_analysis_cache"
    
    id = Column(GUID(), primary_key=True, default=uuid.uuid4)
    url_hash = Column(String(64), unique=True, index=True)
    url = Column(Text, nullable=False)
    analysis_result = Column(JSON, nullable=False)
    confidence_score = Column(Integer, default=0)  # 0-100
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), index=True)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': str(self.id),
            'url_hash': self.url_hash,
            'url': self.url,
            'analysis_result': self.analysis_result,
            'confidence_score': self.confidence_score,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
