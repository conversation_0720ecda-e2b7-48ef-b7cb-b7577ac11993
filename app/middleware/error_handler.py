"""
全局错误处理中间件
"""
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import traceback
import time
from typing import Callable

from app.core.exceptions import LoopHoleException
from app.utils.logger import get_logger

logger = get_logger(__name__)

class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """全局错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable):
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # 记录请求日志
            process_time = time.time() - start_time
            logger.info(
                f"{request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s"
            )
            
            return response
            
        except HTTPException as e:
            # FastAPI HTTP异常
            logger.warning(
                f"{request.method} {request.url.path} - "
                f"HTTP {e.status_code}: {e.detail}"
            )
            
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": {
                        "code": f"HTTP_{e.status_code}",
                        "message": e.detail,
                        "path": str(request.url.path),
                        "method": request.method
                    }
                }
            )
            
        except LoopHoleException as e:
            # 自定义业务异常
            logger.error(
                f"{request.method} {request.url.path} - "
                f"Business Error [{e.error_code}]: {e.message}",
                extra={"details": e.details}
            )
            
            return JSONResponse(
                status_code=400,
                content={
                    "error": {
                        "code": e.error_code,
                        "message": e.message,
                        "details": e.details,
                        "path": str(request.url.path),
                        "method": request.method
                    }
                }
            )
            
        except Exception as e:
            # 未预期的系统异常
            process_time = time.time() - start_time
            error_id = f"ERR_{int(time.time())}"
            
            logger.error(
                f"{request.method} {request.url.path} - "
                f"Unexpected Error [{error_id}]: {str(e)} - "
                f"Time: {process_time:.3f}s",
                extra={
                    "error_id": error_id,
                    "traceback": traceback.format_exc(),
                    "request_body": await self._get_request_body(request)
                }
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "code": "INTERNAL_SERVER_ERROR",
                        "message": "An unexpected error occurred",
                        "error_id": error_id,
                        "path": str(request.url.path),
                        "method": request.method
                    }
                }
            )
    
    async def _get_request_body(self, request: Request) -> str:
        """安全地获取请求体"""
        try:
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
                return body.decode('utf-8')[:1000]  # 限制长度
        except Exception:
            pass
        return ""

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable):
        # 记录请求开始
        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                "client_ip": client_ip,
                "user_agent": user_agent,
                "query_params": dict(request.query_params)
            }
        )
        
        response = await call_next(request)
        
        # 记录请求完成
        process_time = time.time() - start_time
        logger.info(
            f"Request completed: {request.method} {request.url.path} - "
            f"Status: {response.status_code} - Time: {process_time:.3f}s"
        )
        
        return response
