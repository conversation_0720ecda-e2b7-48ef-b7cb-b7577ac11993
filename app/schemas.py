from pydantic import BaseModel, Field, HttpUrl
from typing import Dict, Any, Optional, List
from datetime import datetime
from uuid import UUID

# Task schemas
class TaskCreateRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    url: HttpUrl
    extraction_rules: Dict[str, Any]
    schedule_config: Optional[Dict[str, Any]] = None
    auth_config: Optional[Dict[str, Any]] = None

class TaskUpdateRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    url: Optional[HttpUrl] = None
    extraction_rules: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    auth_config: Optional[Dict[str, Any]] = None
    status: Optional[str] = None

class TaskResponse(BaseModel):
    id: UUID
    name: str
    url: str
    extraction_rules: Dict[str, Any]
    schedule_config: Optional[Dict[str, Any]]
    status: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Extraction schemas
class ExtractionRequest(BaseModel):
    url: HttpUrl
    extraction_rules: Dict[str, Any]
    auth_config: Optional[Dict[str, Any]] = None
    options: Optional[Dict[str, Any]] = None

class AnalysisRequest(BaseModel):
    url: HttpUrl
    auth_config: Optional[Dict[str, Any]] = None
    options: Optional[Dict[str, Any]] = None

class AnalysisResponse(BaseModel):
    analysis_id: str
    url: str
    detected_elements: Dict[str, Any]
    suggested_rules: Dict[str, Any]
    confidence_score: int
    screenshot_url: Optional[str] = None

class ExtractionResponse(BaseModel):
    extraction_id: UUID
    status: str
    extracted_data: Dict[str, Any]
    metadata: Dict[str, Any]
    record_count: int
    extraction_duration: Optional[int]

# Job schemas
class JobResponse(BaseModel):
    job_id: str
    task_id: Optional[UUID]
    status: str
    progress: float = 0.0
    started_at: Optional[datetime]
    estimated_completion: Optional[datetime]
    logs: List[Dict[str, Any]] = []

class JobStatusResponse(BaseModel):
    job_id: str
    status: str
    progress: float = 0.0
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Result schemas
class ResultResponse(BaseModel):
    id: UUID
    task_id: UUID
    job_id: Optional[str]
    data: Dict[str, Any]
    metadata: Optional[Dict[str, Any]]
    record_count: int
    status: str
    extracted_at: datetime

    class Config:
        from_attributes = True

# User schemas
class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=255)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=8)
    full_name: Optional[str] = None
    role: str = "user"

class UserResponse(BaseModel):
    id: UUID
    username: str
    email: str
    full_name: Optional[str]
    role: str
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

class LoginRequest(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

# Common response schemas
class MessageResponse(BaseModel):
    message: str
    details: Optional[Dict[str, Any]] = None

class PaginationResponse(BaseModel):
    page: int
    limit: int
    total: int
    pages: int

class TaskListResponse(BaseModel):
    tasks: List[TaskResponse]
    pagination: PaginationResponse

class ResultListResponse(BaseModel):
    results: List[ResultResponse]
    pagination: PaginationResponse
