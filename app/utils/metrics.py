from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client.core import CollectorRegistry
import time
from functools import wraps
from typing import Dict, Any
import psutil
import os

from app.utils.logger import get_logger

logger = get_logger(__name__)

# 创建自定义注册表
REGISTRY = CollectorRegistry()

# 定义指标
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=REGISTRY
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint'],
    registry=REGISTRY
)

TASK_COUNT = Counter(
    'celery_tasks_total',
    'Total Celery tasks',
    ['task_name', 'status'],
    registry=REGISTRY
)

TASK_DURATION = Histogram(
    'celery_task_duration_seconds',
    'Celery task duration in seconds',
    ['task_name'],
    registry=REGISTRY
)

EXTRACTION_COUNT = Counter(
    'data_extractions_total',
    'Total data extractions',
    ['status'],
    registry=REGISTRY
)

EXTRACTION_RECORDS = Histogram(
    'extraction_records_count',
    'Number of records extracted',
    buckets=[1, 10, 50, 100, 500, 1000, 5000, 10000],
    registry=REGISTRY
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'result'],
    registry=REGISTRY
)

DATABASE_CONNECTIONS = Gauge(
    'database_connections_active',
    'Active database connections',
    registry=REGISTRY
)

SYSTEM_CPU_USAGE = Gauge(
    'system_cpu_usage_percent',
    'System CPU usage percentage',
    registry=REGISTRY
)

SYSTEM_MEMORY_USAGE = Gauge(
    'system_memory_usage_bytes',
    'System memory usage in bytes',
    registry=REGISTRY
)

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.start_time = time.time()
    
    def record_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """记录HTTP请求指标"""
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status=str(status_code)
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_task(self, task_name: str, status: str, duration: float = None):
        """记录Celery任务指标"""
        TASK_COUNT.labels(
            task_name=task_name,
            status=status
        ).inc()
        
        if duration is not None:
            TASK_DURATION.labels(task_name=task_name).observe(duration)
    
    def record_extraction(self, status: str, record_count: int = 0):
        """记录数据提取指标"""
        EXTRACTION_COUNT.labels(status=status).inc()
        
        if record_count > 0:
            EXTRACTION_RECORDS.observe(record_count)
    
    def record_cache_operation(self, operation: str, result: str):
        """记录缓存操作指标"""
        CACHE_OPERATIONS.labels(
            operation=operation,
            result=result
        ).inc()
    
    def update_system_metrics(self):
        """更新系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            SYSTEM_CPU_USAGE.set(cpu_percent)
            
            # 内存使用
            memory = psutil.virtual_memory()
            SYSTEM_MEMORY_USAGE.set(memory.used)
            
            logger.debug(f"System metrics updated: CPU={cpu_percent}%, Memory={memory.used}")
            
        except Exception as e:
            logger.error(f"Failed to update system metrics: {e}")
    
    def get_metrics(self) -> str:
        """获取Prometheus格式的指标"""
        return generate_latest(REGISTRY)

# 全局指标收集器实例
metrics_collector = MetricsCollector()

def track_request_metrics(func):
    """装饰器：跟踪HTTP请求指标"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            response = await func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 从请求中获取信息
            request = kwargs.get('request') or (args[0] if args else None)
            if hasattr(request, 'method') and hasattr(request, 'url'):
                method = request.method
                endpoint = str(request.url.path)
                status_code = getattr(response, 'status_code', 200)
                
                metrics_collector.record_request(method, endpoint, status_code, duration)
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # 记录错误请求
            request = kwargs.get('request') or (args[0] if args else None)
            if hasattr(request, 'method') and hasattr(request, 'url'):
                method = request.method
                endpoint = str(request.url.path)
                
                metrics_collector.record_request(method, endpoint, 500, duration)
            
            raise
    
    return wrapper

def track_task_metrics(task_name: str):
    """装饰器：跟踪Celery任务指标"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                metrics_collector.record_task(task_name, 'success', duration)
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_task(task_name, 'failure', duration)
                raise
        
        return wrapper
    return decorator

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times = []
        self.task_times = []
    
    def start_request_timer(self) -> float:
        """开始请求计时"""
        return time.time()
    
    def end_request_timer(self, start_time: float) -> float:
        """结束请求计时"""
        duration = time.time() - start_time
        self.request_times.append(duration)
        
        # 只保留最近1000个记录
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
        
        return duration
    
    def get_request_stats(self) -> Dict[str, float]:
        """获取请求统计信息"""
        if not self.request_times:
            return {}
        
        times = self.request_times
        return {
            'count': len(times),
            'avg': sum(times) / len(times),
            'min': min(times),
            'max': max(times),
            'p50': self._percentile(times, 50),
            'p95': self._percentile(times, 95),
            'p99': self._percentile(times, 99)
        }
    
    def _percentile(self, data: list, percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory': {
                    'total': psutil.virtual_memory().total,
                    'available': psutil.virtual_memory().available,
                    'percent': psutil.virtual_memory().percent,
                    'used': psutil.virtual_memory().used
                },
                'disk': {
                    'total': psutil.disk_usage('/').total,
                    'used': psutil.disk_usage('/').used,
                    'free': psutil.disk_usage('/').free,
                    'percent': psutil.disk_usage('/').percent
                },
                'process': {
                    'pid': os.getpid(),
                    'threads': psutil.Process().num_threads(),
                    'memory_info': psutil.Process().memory_info()._asdict(),
                    'cpu_percent': psutil.Process().cpu_percent()
                }
            }
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {}

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
