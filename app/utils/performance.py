import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
from functools import wraps
from sqlalchemy import text
from sqlalchemy.orm import Session
from contextlib import asynccontextmanager

from app.database import get_db
from app.core.cache import cache_manager
from app.utils.logger import get_logger

logger = get_logger(__name__)

class QueryOptimizer:
    """数据库查询优化器"""
    
    def __init__(self):
        self.query_cache = {}
        self.slow_queries = []
        self.query_stats = {}
    
    def log_slow_query(self, query: str, duration: float, threshold: float = 1.0):
        """记录慢查询"""
        if duration > threshold:
            slow_query = {
                'query': query,
                'duration': duration,
                'timestamp': time.time()
            }
            self.slow_queries.append(slow_query)
            
            # 只保留最近100个慢查询
            if len(self.slow_queries) > 100:
                self.slow_queries = self.slow_queries[-100:]
            
            logger.warning(f"Slow query detected: {duration:.2f}s - {query[:100]}...")
    
    def get_slow_queries(self) -> List[Dict]:
        """获取慢查询列表"""
        return self.slow_queries
    
    def optimize_task_queries(self, db: Session):
        """优化任务相关查询"""
        try:
            # 创建任务表索引
            db.execute(text("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_status 
                ON tasks(status);
            """))
            
            db.execute(text("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_created_at 
                ON tasks(created_at DESC);
            """))
            
            db.execute(text("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_user_id 
                ON tasks(user_id);
            """))
            
            # 创建结果表索引
            db.execute(text("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_results_task_id 
                ON extraction_results(task_id);
            """))
            
            db.execute(text("""
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_results_created_at 
                ON extraction_results(created_at DESC);
            """))
            
            db.commit()
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create database indexes: {e}")
            db.rollback()

def query_timer(func):
    """查询计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录查询统计
            func_name = func.__name__
            if func_name not in query_optimizer.query_stats:
                query_optimizer.query_stats[func_name] = {
                    'count': 0,
                    'total_time': 0,
                    'avg_time': 0,
                    'max_time': 0
                }
            
            stats = query_optimizer.query_stats[func_name]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['max_time'] = max(stats['max_time'], duration)
            
            # 记录慢查询
            query_optimizer.log_slow_query(func_name, duration)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Query failed after {duration:.2f}s: {func_name} - {e}")
            raise
    
    return wrapper

class CacheOptimizer:
    """缓存优化器"""
    
    def __init__(self):
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
    
    def cache_with_ttl(self, key_prefix: str, ttl: int = 300):
        """带TTL的缓存装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
                
                # 尝试从缓存获取
                cached_result = await cache_manager.get(cache_key)
                if cached_result is not None:
                    self.cache_stats['hits'] += 1
                    logger.debug(f"Cache hit for key: {cache_key}")
                    return cached_result
                
                # 缓存未命中，执行函数
                self.cache_stats['misses'] += 1
                result = await func(*args, **kwargs)
                
                # 存储到缓存
                await cache_manager.set(cache_key, result, ttl)
                self.cache_stats['sets'] += 1
                logger.debug(f"Cache set for key: {cache_key}")
                
                return result
            
            return wrapper
        return decorator
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            'hit_rate': hit_rate,
            'total_requests': total_requests
        }

class ConnectionPoolOptimizer:
    """连接池优化器"""
    
    def __init__(self):
        self.pool_stats = {
            'active_connections': 0,
            'idle_connections': 0,
            'total_connections': 0
        }
    
    @asynccontextmanager
    async def get_optimized_db(self):
        """获取优化的数据库连接"""
        db = next(get_db())
        try:
            self.pool_stats['active_connections'] += 1
            yield db
        finally:
            self.pool_stats['active_connections'] -= 1
            db.close()
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取连接池统计"""
        return self.pool_stats

class BatchProcessor:
    """批处理优化器"""
    
    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
    
    async def process_in_batches(self, items: List[Any], processor: Callable):
        """批量处理数据"""
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_results = await processor(batch)
            results.extend(batch_results)
            
            # 避免阻塞事件循环
            await asyncio.sleep(0.01)
        
        return results
    
    async def bulk_insert(self, db: Session, model_class, data_list: List[Dict]):
        """批量插入数据"""
        try:
            # 分批插入
            for i in range(0, len(data_list), self.batch_size):
                batch = data_list[i:i + self.batch_size]
                db.bulk_insert_mappings(model_class, batch)
                
                # 每批提交一次
                if i % (self.batch_size * 5) == 0:
                    db.commit()
            
            db.commit()
            logger.info(f"Bulk inserted {len(data_list)} records")
            
        except Exception as e:
            db.rollback()
            logger.error(f"Bulk insert failed: {e}")
            raise

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.memory_usage = {}
    
    def track_memory_usage(self, func_name: str):
        """跟踪内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        self.memory_usage[func_name] = {
            'rss': memory_info.rss,
            'vms': memory_info.vms,
            'timestamp': time.time()
        }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'current_rss': memory_info.rss,
            'current_vms': memory_info.vms,
            'memory_percent': process.memory_percent(),
            'tracked_functions': self.memory_usage
        }
    
    def memory_profiler(self, func):
        """内存分析装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            
            # 记录执行前内存
            self.track_memory_usage(f"{func_name}_before")
            
            try:
                result = func(*args, **kwargs)
                
                # 记录执行后内存
                self.track_memory_usage(f"{func_name}_after")
                
                return result
                
            except Exception as e:
                self.track_memory_usage(f"{func_name}_error")
                raise
        
        return wrapper

# 全局优化器实例
query_optimizer = QueryOptimizer()
cache_optimizer = CacheOptimizer()
connection_pool_optimizer = ConnectionPoolOptimizer()
batch_processor = BatchProcessor()
memory_optimizer = MemoryOptimizer()

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.analysis_results = {}
    
    def analyze_query_performance(self) -> Dict[str, Any]:
        """分析查询性能"""
        return {
            'slow_queries': query_optimizer.get_slow_queries(),
            'query_stats': query_optimizer.query_stats,
            'recommendations': self._get_query_recommendations()
        }
    
    def analyze_cache_performance(self) -> Dict[str, Any]:
        """分析缓存性能"""
        cache_stats = cache_optimizer.get_cache_stats()
        
        return {
            'cache_stats': cache_stats,
            'recommendations': self._get_cache_recommendations(cache_stats)
        }
    
    def analyze_memory_performance(self) -> Dict[str, Any]:
        """分析内存性能"""
        memory_stats = memory_optimizer.get_memory_stats()
        
        return {
            'memory_stats': memory_stats,
            'recommendations': self._get_memory_recommendations(memory_stats)
        }
    
    def _get_query_recommendations(self) -> List[str]:
        """获取查询优化建议"""
        recommendations = []
        
        slow_queries = query_optimizer.get_slow_queries()
        if len(slow_queries) > 10:
            recommendations.append("检测到大量慢查询，建议优化数据库索引")
        
        for func_name, stats in query_optimizer.query_stats.items():
            if stats['avg_time'] > 0.5:
                recommendations.append(f"函数 {func_name} 平均执行时间过长，建议优化")
        
        return recommendations
    
    def _get_cache_recommendations(self, cache_stats: Dict) -> List[str]:
        """获取缓存优化建议"""
        recommendations = []
        
        if cache_stats['hit_rate'] < 50:
            recommendations.append("缓存命中率较低，建议调整缓存策略")
        
        if cache_stats['total_requests'] > 1000 and cache_stats['hit_rate'] < 80:
            recommendations.append("高频访问但缓存效果不佳，建议增加缓存时间")
        
        return recommendations
    
    def _get_memory_recommendations(self, memory_stats: Dict) -> List[str]:
        """获取内存优化建议"""
        recommendations = []
        
        if memory_stats['memory_percent'] > 80:
            recommendations.append("内存使用率过高，建议优化内存使用")
        
        return recommendations
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        return {
            'query_analysis': self.analyze_query_performance(),
            'cache_analysis': self.analyze_cache_performance(),
            'memory_analysis': self.analyze_memory_performance(),
            'timestamp': time.time()
        }

# 全局性能分析器实例
performance_analyzer = PerformanceAnalyzer()
