from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from contextlib import asynccontextmanager

from app.api.v1 import tasks, extract, auth, health, results, jobs, metrics, performance, websocket, alerts, dashboard
from app.database import engine, Base
from app.utils.logger import configure_logging
from app.core.alert_manager import alert_manager

# Configure logging
configure_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 Starting Loop Hole API...")
    
    # Create database tables
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created")
    
    # Start alert manager
    import asyncio
    asyncio.create_task(alert_manager.start_monitoring())
    print("✅ Alert manager started")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Loop Hole API...")
    alert_manager.stop_monitoring()
    print("✅ Alert manager stopped")

# Create FastAPI application
app = FastAPI(
    title="Loop Hole API",
    description="智能网页数据提取系统 API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境中应该限制具体主机
)

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "Internal server error",
                "details": str(exc) if os.getenv("DEBUG") else None
            }
        }
    )

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["tasks"])
app.include_router(extract.router, prefix="/api/v1/extract", tags=["extraction"])
app.include_router(results.router, prefix="/api/v1/results", tags=["results"])
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["jobs"])
app.include_router(metrics.router, prefix="/api/v1", tags=["metrics"])
app.include_router(performance.router, prefix="/api/v1", tags=["performance"])
app.include_router(websocket.router, prefix="/api/v1", tags=["websocket"])
app.include_router(alerts.router, prefix="/api/v1/alerts", tags=["alerts"])
app.include_router(dashboard.router, prefix="/api/v1", tags=["dashboard"])

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to Loop Hole API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health"
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
