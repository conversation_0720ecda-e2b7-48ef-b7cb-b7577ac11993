from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import Static<PERSON>ool
import os
from pathlib import Path

# Database configuration - 默认使用SQLite，支持PostgreSQL
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./data/loop_hole.db")

def create_database_engine(database_url: str = None):
    """创建数据库引擎，支持SQLite和PostgreSQL"""
    if database_url is None:
        database_url = DATABASE_URL
    
    if "sqlite" in database_url:
        # SQLite配置
        # 确保数据目录存在
        db_path = database_url.replace("sqlite:///", "")
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        return create_engine(
            database_url,
            connect_args={
                "check_same_thread": False,
                "timeout": 20
            },
            poolclass=StaticPool,
            echo=False
        )
    else:
        # PostgreSQL配置
        return create_engine(
            database_url,
            pool_pre_ping=True,
            pool_size=10,
            max_overflow=20,
            echo=False
        )

# Create SQLAlchemy engine
engine = create_database_engine()

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Async database session for FastAPI
async def get_db_session():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
