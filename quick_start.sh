#!/bin/bash

# Loop Hole 快速启动脚本
# 此脚本将自动部署和启动 Loop Hole 智能网页数据提取系统

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "等待 $service_name 启动..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f $url > /dev/null 2>&1; then
            log_success "$service_name 已启动"
            return 0
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "$service_name 启动超时"
    return 1
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    cat << 'EOF'
 _                       _   _       _
| |                     | | | |     | |
| |     ___   ___  _ __ | |_| | ___ | | ___
| |    / _ \ / _ \| '_ \|  _  |/ _ \| |/ _ \
| |___| (_) | (_) | |_) | | | | (_) | |  __/
\_____/\___/ \___/| .__/\_| |_/\___/|_|\___|
                  | |
                  |_|

智能网页数据提取系统 - 快速启动脚本
EOF
    echo -e "${NC}"
}

# 检查系统环境
check_prerequisites() {
    log_info "检查系统环境..."

    # 检查操作系统
    OS="$(uname -s)"
    case "${OS}" in
        Linux*)     MACHINE=Linux;;
        Darwin*)    MACHINE=Mac;;
        CYGWIN*)    MACHINE=Cygwin;;
        MINGW*)     MACHINE=MinGw;;
        *)          MACHINE="UNKNOWN:${OS}";;
    esac
    log_info "检测到操作系统: $MACHINE"

    # 检查Docker
    if ! check_command docker; then
        log_error "Docker 未安装或不在 PATH 中"
        log_info "请先安装 Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    log_success "Docker 已安装: $(docker --version)"

    # 检查Docker Compose
    if ! check_command docker-compose && ! docker compose version > /dev/null 2>&1; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        log_info "请先安装 Docker Compose"
        exit 1
    fi

    # 优先使用 docker compose (新版本)
    if docker compose version > /dev/null 2>&1; then
        DOCKER_COMPOSE="docker compose"
        log_success "Docker Compose 已安装: $(docker compose version --short)"
    else
        DOCKER_COMPOSE="docker-compose"
        log_success "Docker Compose 已安装: $(docker-compose --version)"
    fi

    # 检查Git
    if ! check_command git; then
        log_warning "Git 未安装，将跳过版本检查"
    else
        log_success "Git 已安装: $(git --version)"
    fi

    # 检查端口占用
    PORTS=(80 8000 5432 6379)
    OCCUPIED_PORTS=()

    for port in "${PORTS[@]}"; do
        if ! check_port $port; then
            OCCUPIED_PORTS+=($port)
        fi
    done

    if [ ${#OCCUPIED_PORTS[@]} -gt 0 ]; then
        log_warning "以下端口被占用: ${OCCUPIED_PORTS[*]}"
        log_warning "这可能会导致服务启动失败"
        echo -n "是否继续? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "已取消启动"
            exit 0
        fi
    fi

    log_success "环境检查完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."

    if [ ! -f .env ]; then
        cat > .env << 'EOF'
# 数据库配置
DATABASE_URL=*******************************************************/loop_hole

# Redis配置
REDIS_URL=redis://redis:6379/0

# 应用配置
SECRET_KEY=loop-hole-secret-key-change-in-production
DEBUG=False
ENVIRONMENT=production
API_HOST=0.0.0.0
API_PORT=8000

# Celery配置
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_WORKER_CONCURRENCY=4

# 数据库配置
POSTGRES_DB=loop_hole
POSTGRES_USER=loop_hole
POSTGRES_PASSWORD=loop_hole_password

# 前端配置
VITE_API_BASE_URL=http://localhost:8000
EOF
        log_success "已创建 .env 文件"
    else
        log_info ".env 文件已存在，跳过创建"
    fi
}

# 创建开发环境配置
create_dev_compose() {
    if [ ! -f docker-compose.dev.yml ]; then
        log_info "创建开发环境配置文件..."
        cat > docker-compose.dev.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:13-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG}
      - ENVIRONMENT=${ENVIRONMENT}
    volumes:
      - ./app:/app/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  worker:
    build: .
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - CELERY_WORKER_CONCURRENCY=${CELERY_WORKER_CONCURRENCY}
    volumes:
      - ./app:/app/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A app.core.tasks worker --loglevel=info --concurrency=${CELERY_WORKER_CONCURRENCY}

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - web
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}

volumes:
  postgres_data:
EOF
        log_success "已创建开发环境配置文件"
    fi
}

# 启动服务
start_services() {
    log_info "构建和启动服务..."

    # 拉取基础镜像
    log_info "拉取基础镜像..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml pull postgres redis

    # 构建应用镜像
    log_info "构建应用镜像..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml build

    # 启动服务
    log_info "启动服务..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml up -d

    # 等待服务启动
    log_info "等待服务启动完成..."
    sleep 5

    # 检查服务状态
    log_info "检查服务状态..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml ps
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."

    # 等待数据库准备就绪
    wait_for_service "http://localhost:8000/api/v1/health" "后端API"

    # 运行数据库迁移
    log_info "运行数据库迁移..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml exec web alembic upgrade head

    log_success "数据库初始化完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."

    # 检查后端API
    if wait_for_service "http://localhost:8000/api/v1/health" "后端API"; then
        log_success "后端API运行正常"
    else
        log_error "后端API启动失败"
        show_logs
        exit 1
    fi

    # 检查前端
    if wait_for_service "http://localhost" "前端界面"; then
        log_success "前端界面运行正常"
    else
        log_warning "前端界面可能需要更多时间启动"
    fi

    # 检查数据库连接
    log_info "检查数据库连接..."
    if $DOCKER_COMPOSE -f docker-compose.dev.yml exec postgres pg_isready -U loop_hole > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
    fi

    # 检查Redis连接
    log_info "检查Redis连接..."
    if $DOCKER_COMPOSE -f docker-compose.dev.yml exec redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
    fi

    log_success "部署验证完成"
}

# 显示访问信息
show_access_info() {
    echo
    log_success "🎉 Loop Hole 系统启动成功！"
    echo
    echo -e "${GREEN}访问地址:${NC}"
    echo -e "  前端界面: ${BLUE}http://localhost${NC}"
    echo -e "  API文档:  ${BLUE}http://localhost:8000/docs${NC}"
    echo -e "  API接口: ${BLUE}http://localhost:8000${NC}"
    echo
    echo -e "${GREEN}常用命令:${NC}"
    echo -e "  查看日志: ${YELLOW}$DOCKER_COMPOSE -f docker-compose.dev.yml logs -f${NC}"
    echo -e "  停止服务: ${YELLOW}$DOCKER_COMPOSE -f docker-compose.dev.yml down${NC}"
    echo -e "  重启服务: ${YELLOW}$DOCKER_COMPOSE -f docker-compose.dev.yml restart${NC}"
    echo -e "  查看状态: ${YELLOW}$DOCKER_COMPOSE -f docker-compose.dev.yml ps${NC}"
    echo
    echo -e "${GREEN}快速开始:${NC}"
    echo -e "  1. 打开浏览器访问: ${BLUE}http://localhost${NC}"
    echo -e "  2. 点击 '任务管理' 创建第一个数据提取任务"
    echo -e "  3. 输入目标网站URL，配置提取规则"
    echo -e "  4. 点击 '测试提取' 验证配置"
    echo -e "  5. 保存并执行任务"
    echo
    echo -e "${GREEN}获取帮助:${NC}"
    echo -e "  详细使用指南: ${BLUE}docs/user_guide.md${NC}"
    echo -e "  API文档: ${BLUE}http://localhost:8000/docs${NC}"
    echo
}

# 显示错误日志
show_logs() {
    log_error "启动失败，显示最近日志："
    echo "----------------------------------------"
    $DOCKER_COMPOSE -f docker-compose.dev.yml logs --tail=20
    echo "----------------------------------------"
    log_info "查看完整日志: $DOCKER_COMPOSE -f docker-compose.dev.yml logs"
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    if [ -n "$DOCKER_COMPOSE" ]; then
        $DOCKER_COMPOSE -f docker-compose.dev.yml down
    fi
}

# 主函数
main() {
    # 捕获退出信号
    trap cleanup EXIT INT TERM

    show_banner

    # 检查是否在项目目录中
    if [ ! -f "requirements.txt" ] || [ ! -d "app" ]; then
        log_error "请在 Loop Hole 项目根目录中运行此脚本"
        log_info "当前目录: $(pwd)"
        log_info "请确保目录包含 requirements.txt 和 app/ 文件夹"
        exit 1
    fi

    log_info "开始启动 Loop Hole 系统..."

    # 执行启动流程
    check_prerequisites
    create_env_file
    create_dev_compose
    start_services

    # 等待服务完全启动
    log_info "等待服务完全启动 (30秒)..."
    sleep 30

    init_database
    verify_deployment
    show_access_info

    log_success "启动完成！系统已准备就绪。"
}

# 脚本选项处理
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            echo "Loop Hole 快速启动脚本"
            echo
            echo "用法: $0 [选项]"
            echo
            echo "选项:"
            echo "  -h, --help     显示此帮助信息"
            echo "  --logs         显示服务日志"
            echo "  --stop         停止所有服务"
            echo "  --restart      重启所有服务"
            echo "  --status       显示服务状态"
            echo "  --clean        清理所有数据和容器"
            echo
            exit 0
            ;;
        --logs)
            docker-compose -f docker-compose.dev.yml logs -f
            exit 0
            ;;
        --stop)
            log_info "停止所有服务..."
            docker-compose -f docker-compose.dev.yml down
            log_success "服务已停止"
            exit 0
            ;;
        --restart)
            log_info "重启所有服务..."
            docker-compose -f docker-compose.dev.yml restart
            log_success "服务已重启"
            exit 0
            ;;
        --status)
            docker-compose -f docker-compose.dev.yml ps
            exit 0
            ;;
        --clean)
            echo -n "确定要清理所有数据和容器吗? [y/N]: "
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                log_info "清理所有数据和容器..."
                docker-compose -f docker-compose.dev.yml down -v --remove-orphans
                docker system prune -f
                log_success "清理完成"
            else
                log_info "已取消清理"
            fi
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            log_info "使用 $0 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 运行主函数
main
