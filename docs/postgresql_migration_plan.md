# Loop Hole - PostgreSQL 迁移计划

## 📋 概述

本文档提供从SQLite迁移到PostgreSQL的**简化迁移方案**，重点关注操作简单、不易出错的步骤。

## 🎯 迁移触发条件

### 必须迁移的情况
- [ ] 多用户同时使用（>3人）
- [ ] 数据量超过100MB
- [ ] 并发任务数超过10个
- [ ] 需要高可用性部署

### 建议迁移的情况
- [ ] 进入生产环境
- [ ] 需要复杂的JSON查询
- [ ] 团队规模扩大
- [ ] 需要数据分析功能

## 🛠️ 迁移前准备

### 1. 环境检查
```bash
# 检查当前数据库状态
ls -la data/loop_hole.db
sqlite3 data/loop_hole.db "SELECT count(*) FROM extraction_tasks;"
sqlite3 data/loop_hole.db "SELECT count(*) FROM extraction_results;"
```

### 2. 数据备份
```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d)

# 备份SQLite数据库文件
cp data/loop_hole.db backup/$(date +%Y%m%d)/loop_hole_backup.db

# 导出SQL格式备份
sqlite3 data/loop_hole.db .dump > backup/$(date +%Y%m%d)/loop_hole_backup.sql

echo "备份完成：backup/$(date +%Y%m%d)/"
```

### 3. PostgreSQL环境准备
```bash
# 方式1：使用Docker（推荐）
docker-compose up -d postgres

# 方式2：本地安装PostgreSQL
# macOS: brew install postgresql
# Ubuntu: sudo apt-get install postgresql

# 验证PostgreSQL连接
docker exec -it loop_hole_postgres psql -U postgres -c "SELECT version();"
```

## 🔄 简化迁移步骤

### 步骤1：安装PostgreSQL依赖
```bash
# 取消注释PostgreSQL驱动
sed -i '' 's/# psycopg2-binary/psycopg2-binary/' requirements.txt

# 安装依赖
pip install psycopg2-binary==2.9.9
```

### 步骤2：创建PostgreSQL数据库
```bash
# 创建数据库
docker exec -it loop_hole_postgres createdb -U postgres loop_hole

# 验证数据库创建
docker exec -it loop_hole_postgres psql -U postgres -l | grep loop_hole
```

### 步骤3：更新环境配置
```bash
# 创建PostgreSQL环境配置
cp .env.example .env.postgresql

# 编辑配置文件
cat > .env.postgresql << EOF
# PostgreSQL配置
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/loop_hole
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key
DEBUG=false
LOG_LEVEL=info
EOF
```

### 步骤4：运行数据库迁移
```bash
# 使用PostgreSQL配置
export $(cat .env.postgresql | xargs)

# 创建数据库表结构
alembic upgrade head

# 验证表结构
docker exec -it loop_hole_postgres psql -U postgres -d loop_hole -c "\dt"
```

### 步骤5：数据迁移（自动化脚本）

创建迁移脚本：
```python
# scripts/migrate_sqlite_to_postgres.py
import sqlite3
import psycopg2
import json
from datetime import datetime
import os

def migrate_data():
    # SQLite连接
    sqlite_conn = sqlite3.connect('data/loop_hole.db')
    sqlite_conn.row_factory = sqlite3.Row
    
    # PostgreSQL连接
    pg_conn = psycopg2.connect(
        host='localhost',
        database='loop_hole',
        user='postgres',
        password='postgres'
    )
    
    try:
        # 迁移用户表
        migrate_users(sqlite_conn, pg_conn)
        
        # 迁移任务表
        migrate_tasks(sqlite_conn, pg_conn)
        
        # 迁移结果表
        migrate_results(sqlite_conn, pg_conn)
        
        pg_conn.commit()
        print("数据迁移完成！")
        
    except Exception as e:
        pg_conn.rollback()
        print(f"迁移失败：{e}")
        raise
    finally:
        sqlite_conn.close()
        pg_conn.close()

def migrate_users(sqlite_conn, pg_conn):
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT * FROM users")
    
    pg_cursor = pg_conn.cursor()
    
    for row in cursor.fetchall():
        pg_cursor.execute("""
            INSERT INTO users (id, username, email, hashed_password, is_active, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
            ON CONFLICT (id) DO NOTHING
        """, (
            row['id'], row['username'], row['email'], 
            row['hashed_password'], row['is_active'], row['created_at']
        ))
    
    print(f"迁移用户数据：{cursor.rowcount} 条记录")

def migrate_tasks(sqlite_conn, pg_conn):
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT * FROM extraction_tasks")
    
    pg_cursor = pg_conn.cursor()
    
    for row in cursor.fetchall():
        pg_cursor.execute("""
            INSERT INTO extraction_tasks (
                id, name, url, extraction_rules, schedule_config, 
                auth_config, status, is_active, created_at, updated_at, 
                created_by, last_run, last_success, total_runs, next_run, error_message
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (id) DO NOTHING
        """, (
            row['id'], row['name'], row['url'], 
            json.dumps(row['extraction_rules']) if row['extraction_rules'] else None,
            json.dumps(row['schedule_config']) if row['schedule_config'] else None,
            json.dumps(row['auth_config']) if row['auth_config'] else None,
            row['status'], row['is_active'], row['created_at'], row['updated_at'],
            row['created_by'], row['last_run'], row['last_success'], 
            row['total_runs'], row['next_run'], row['error_message']
        ))
    
    print(f"迁移任务数据：{cursor.rowcount} 条记录")

def migrate_results(sqlite_conn, pg_conn):
    cursor = sqlite_conn.cursor()
    cursor.execute("SELECT * FROM extraction_results")
    
    pg_cursor = pg_conn.cursor()
    
    for row in cursor.fetchall():
        pg_cursor.execute("""
            INSERT INTO extraction_results (
                id, task_id, data, metadata, extracted_at, data_hash
            ) VALUES (%s, %s, %s, %s, %s, %s)
            ON CONFLICT (id) DO NOTHING
        """, (
            row['id'], row['task_id'],
            json.dumps(row['data']) if row['data'] else None,
            json.dumps(row['metadata']) if row['metadata'] else None,
            row['extracted_at'], row['data_hash']
        ))
    
    print(f"迁移结果数据：{cursor.rowcount} 条记录")

if __name__ == "__main__":
    migrate_data()
```

执行迁移：
```bash
# 运行迁移脚本
python scripts/migrate_sqlite_to_postgres.py
```

### 步骤6：验证迁移结果
```bash
# 检查数据完整性
docker exec -it loop_hole_postgres psql -U postgres -d loop_hole << EOF
SELECT 'users' as table_name, count(*) as count FROM users
UNION ALL
SELECT 'extraction_tasks', count(*) FROM extraction_tasks
UNION ALL
SELECT 'extraction_results', count(*) FROM extraction_results;
EOF

# 测试应用连接
export $(cat .env.postgresql | xargs)
python -c "from app.database import engine; print('PostgreSQL连接成功！')"
```

### 步骤7：切换应用配置
```bash
# 备份当前配置
cp .env .env.sqlite.backup

# 使用PostgreSQL配置
cp .env.postgresql .env

# 重启应用
docker-compose restart app
```

## 🔙 回滚方案

如果迁移出现问题，可以快速回滚：

```bash
# 1. 恢复SQLite配置
cp .env.sqlite.backup .env

# 2. 恢复数据库文件（如果需要）
cp backup/$(date +%Y%m%d)/loop_hole_backup.db data/loop_hole.db

# 3. 重启应用
docker-compose restart app

echo "已回滚到SQLite配置"
```

## ✅ 迁移检查清单

### 迁移前检查
- [ ] SQLite数据已备份
- [ ] PostgreSQL服务正常运行
- [ ] 网络连接正常
- [ ] 磁盘空间充足

### 迁移过程检查
- [ ] PostgreSQL依赖安装成功
- [ ] 数据库创建成功
- [ ] 表结构迁移成功
- [ ] 数据迁移无错误
- [ ] 数据完整性验证通过

### 迁移后检查
- [ ] 应用启动正常
- [ ] 用户登录功能正常
- [ ] 任务创建功能正常
- [ ] 数据提取功能正常
- [ ] API接口响应正常

## 🚨 常见问题解决

### 问题1：PostgreSQL连接失败
```bash
# 检查PostgreSQL服务状态
docker ps | grep postgres

# 检查端口占用
lsof -i :5432

# 重启PostgreSQL服务
docker-compose restart postgres
```

### 问题2：数据迁移失败
```bash
# 检查SQLite数据库
sqlite3 data/loop_hole.db ".schema"

# 检查PostgreSQL表结构
docker exec -it loop_hole_postgres psql -U postgres -d loop_hole -c "\d"

# 手动清理PostgreSQL数据
docker exec -it loop_hole_postgres psql -U postgres -d loop_hole -c "TRUNCATE users, extraction_tasks, extraction_results CASCADE;"
```

### 问题3：应用启动失败
```bash
# 检查环境变量
echo $DATABASE_URL

# 检查依赖安装
pip list | grep psycopg2

# 查看详细错误日志
docker-compose logs app
```

## 📊 性能优化建议

迁移完成后的PostgreSQL优化：

```sql
-- 创建索引
CREATE INDEX idx_extraction_tasks_status ON extraction_tasks(status);
CREATE INDEX idx_extraction_tasks_created_by ON extraction_tasks(created_by);
CREATE INDEX idx_extraction_results_task_id ON extraction_results(task_id);
CREATE INDEX idx_extraction_results_extracted_at ON extraction_results(extracted_at);

-- JSONB字段索引
CREATE INDEX idx_extraction_rules_gin ON extraction_tasks USING GIN(extraction_rules);
CREATE INDEX idx_extraction_data_gin ON extraction_results USING GIN(data);

-- 更新统计信息
ANALYZE;
```

## 📝 迁移记录模板

```
迁移日期：____年__月__日
迁移人员：__________
迁移原因：__________

迁移前状态：
- SQLite文件大小：____MB
- 用户数量：____
- 任务数量：____
- 结果数量：____

迁移后状态：
- PostgreSQL连接：正常/异常
- 数据完整性：通过/失败
- 应用功能：正常/异常

问题记录：
- 问题描述：__________
- 解决方案：__________

备注：__________
```

---

**文档版本**：v1.0  
**最后更新**：2024年1月  
**维护者**：Loop Hole开发团队

> 💡 **提示**：建议在非生产环境先进行完整的迁移测试，确保流程无误后再在生产环境执行。