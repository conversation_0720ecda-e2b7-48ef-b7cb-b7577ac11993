# Loop Hole 故障排除指南

## 📋 目录

1. [快速诊断](#快速诊断)
2. [安装和启动问题](#安装和启动问题)
3. [服务连接问题](#服务连接问题)
4. [数据提取问题](#数据提取问题)
5. [性能问题](#性能问题)
6. [前端界面问题](#前端界面问题)
7. [API接口问题](#api接口问题)
8. [Docker相关问题](#docker相关问题)
9. [数据库问题](#数据库问题)
10. [日志分析](#日志分析)
11. [系统监控](#系统监控)
12. [紧急恢复](#紧急恢复)

---

## 快速诊断

### 系统健康检查脚本

```bash
#!/bin/bash
# 保存为 health_check.sh 并运行

echo "=== Loop Hole 系统健康检查 ==="

# 检查Docker容器状态
echo "1. Docker容器状态："
docker-compose -f docker-compose.dev.yml ps

# 检查服务健康状态
echo -e "\n2. 服务健康检查："

# 后端API
if curl -s -f http://localhost:8000/api/v1/health > /dev/null; then
    echo "✅ 后端API: 正常"
else
    echo "❌ 后端API: 异常"
fi

# 前端
if curl -s -f http://localhost > /dev/null; then
    echo "✅ 前端界面: 正常"
else
    echo "❌ 前端界面: 异常"
fi

# 数据库
if docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U loop_hole > /dev/null 2>&1; then
    echo "✅ 数据库: 正常"
else
    echo "❌ 数据库: 异常"
fi

# Redis
if docker-compose -f docker-compose.dev.yml exec redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 异常"
fi

# 检查资源使用
echo -e "\n3. 资源使用情况："
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# 检查磁盘空间
echo -e "\n4. 磁盘空间："
df -h | grep -E "(/$|/var/lib/docker)"

echo -e "\n检查完成！"
```

### Windows 健康检查脚本

```batch
@echo off
REM 保存为 health_check.bat 并运行

echo === Loop Hole 系统健康检查 ===

echo 1. Docker容器状态：
docker-compose -f docker-compose.dev.yml ps

echo.
echo 2. 服务健康检查：

REM 后端API
curl -s -f http://localhost:8000/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端API: 正常
) else (
    echo ❌ 后端API: 异常
)

REM 前端
curl -s -f http://localhost >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端界面: 正常
) else (
    echo ❌ 前端界面: 异常
)

echo.
echo 3. 资源使用情况：
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo.
echo 检查完成！
pause
```

---

## 安装和启动问题

### 问题1: Docker未安装或版本过低

**现象**: 
```bash
bash: docker: command not found
# 或
docker: Error response from daemon: client version 1.24 is too old
```

**解决方案**:
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# macOS
brew install docker
# 或下载 Docker Desktop

# Windows
# 下载并安装 Docker Desktop for Windows

# 验证安装
docker --version
docker-compose --version
```

### 问题2: 权限不足

**现象**: 
```bash
docker: Got permission denied while trying to connect to the Docker daemon socket
```

**解决方案**:
```bash
# Linux: 将用户添加到docker组
sudo usermod -aG docker $USER
newgrp docker

# 或使用sudo运行
sudo docker-compose up -d
```

### 问题3: 端口被占用

**现象**:
```bash
ERROR: for web  Cannot start service web: driver failed programming external connectivity
bind: address already in use
```

**解决方案**:
```bash
# 查找占用端口的进程
sudo lsof -i :80
sudo lsof -i :8000
sudo lsof -i :5432
sudo lsof -i :6379

# 杀死占用进程
sudo kill -9 <PID>

# 或修改docker-compose.yml中的端口映射
ports:
  - "8080:80"    # 将80改为8080
  - "8001:8000"  # 将8000改为8001
```

### 问题4: 内存不足

**现象**:
```bash
ERROR: for postgres  Cannot start service postgres: OCI runtime create failed
```

**解决方案**:
```bash
# 检查内存使用
free -h

# 释放缓存
sudo sync && sudo sh -c 'echo 3 > /proc/sys/vm/drop_caches'

# 减少Docker内存限制
# 在docker-compose.yml中添加：
services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

---

## 服务连接问题

### 问题1: 数据库连接失败

**现象**:
```python
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) could not connect to server
```

**诊断步骤**:
```bash
# 1. 检查PostgreSQL容器状态
docker-compose -f docker-compose.dev.yml ps postgres

# 2. 检查PostgreSQL日志
docker-compose -f docker-compose.dev.yml logs postgres

# 3. 测试数据库连接
docker-compose -f docker-compose.dev.yml exec postgres psql -U loop_hole -d loop_hole -c "SELECT 1;"

# 4. 检查环境变量
docker-compose -f docker-compose.dev.yml exec web env | grep DATABASE_URL
```

**解决方案**:
```bash
# 重启数据库服务
docker-compose -f docker-compose.dev.yml restart postgres

# 如果持续失败，删除数据卷重新创建
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d

# 检查.env文件中的数据库配置
cat .env | grep DATABASE_URL
```

### 问题2: Redis连接失败

**现象**:
```python
redis.exceptions.ConnectionError: Error connecting to Redis
```

**诊断步骤**:
```bash
# 1. 检查Redis容器状态
docker-compose -f docker-compose.dev.yml ps redis

# 2. 测试Redis连接
docker-compose -f docker-compose.dev.yml exec redis redis-cli ping

# 3. 检查Redis日志
docker-compose -f docker-compose.dev.yml logs redis

# 4. 检查网络连接
docker-compose -f docker-compose.dev.yml exec web ping redis
```

**解决方案**:
```bash
# 重启Redis服务
docker-compose -f docker-compose.dev.yml restart redis

# 检查Redis配置
docker-compose -f docker-compose.dev.yml exec redis redis-cli config get "*"

# 清空Redis缓存
docker-compose -f docker-compose.dev.yml exec redis redis-cli flushall
```

### 问题3: Celery工作进程无法启动

**现象**:
```python
[ERROR/MainProcess] consumer: Cannot connect to redis://redis:6379/0: 
Error connecting to Redis
```

**诊断步骤**:
```bash
# 1. 检查worker容器状态
docker-compose -f docker-compose.dev.yml ps worker

# 2. 查看worker日志
docker-compose -f docker-compose.dev.yml logs worker

# 3. 手动测试Celery连接
docker-compose -f docker-compose.dev.yml exec worker celery -A app.core.tasks inspect ping
```

**解决方案**:
```bash
# 重启worker服务
docker-compose -f docker-compose.dev.yml restart worker

# 检查Celery配置
docker-compose -f docker-compose.dev.yml exec worker python -c "from app.core.tasks import celery_app; print(celery_app.conf)"

# 清理Celery队列
docker-compose -f docker-compose.dev.yml exec redis redis-cli del celery
```

---

## 数据提取问题

### 问题1: 页面加载失败

**现象**:
```json
{
  "error": "Page load timeout",
  "details": "Failed to load https://example.com within 30000ms"
}
```

**解决方案**:
```python
# 1. 增加等待时间
{
  "wait_config": {
    "timeout": 60000,  # 增加到60秒
    "wait_for_selector": ".content"  # 等待特定元素
  }
}

# 2. 添加用户代理
{
  "browser_config": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  }
}

# 3. 配置代理
{
  "proxy_config": {
    "server": "http://proxy.example.com:8080",
    "username": "user",
    "password": "pass"
  }
}
```

### 问题2: 数据提取为空

**现象**:
```json
{
  "status": "success",
  "data": {},
  "message": "No data extracted"
}
```

**诊断步骤**:
```bash
# 1. 在浏览器中检查页面结构
# 按F12打开开发者工具，检查HTML结构

# 2. 测试CSS选择器
document.querySelectorAll('你的选择器')

# 3. 检查页面是否需要JavaScript渲染
# 禁用JavaScript后刷新页面，查看内容是否还在
```

**解决方案**:
```python
# 1. 调整选择器
{
  "extraction_rules": {
    "type": "manual",
    "rules": [
      {
        "field_name": "title",
        "selector": "h1, .title, [data-title]",  # 使用多个选择器
        "attribute": "text"
      }
    ]
  }
}

# 2. 等待页面渲染
{
  "wait_config": {
    "wait_for_selector": ".dynamic-content",
    "timeout": 30000
  }
}

# 3. 模拟用户交互
{
  "actions": [
    {
      "type": "click",
      "selector": ".load-more-btn"
    },
    {
      "type": "wait",
      "duration": 2000
    }
  ]
}
```

### 问题3: 反爬虫检测

**现象**:
```json
{
  "error": "Access denied",
  "status_code": 403,
  "message": "Blocked by anti-bot system"
}
```

**解决方案**:
```python
# 1. 配置浏览器参数
{
  "browser_config": {
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "viewport": {"width": 1920, "height": 1080},
    "extra_http_headers": {
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
    }
  }
}

# 2. 添加随机延迟
{
  "timing_config": {
    "random_delay": true,
    "min_delay": 1000,
    "max_delay": 5000
  }
}

# 3. 使用认证
{
  "auth_config": {
    "type": "cookie",
    "cookies": "session=abc123; token=xyz789"
  }
}
```

---

## 性能问题

### 问题1: 系统响应慢

**诊断步骤**:
```bash
# 1. 检查系统资源
docker stats --no-stream

# 2. 检查数据库性能
docker-compose -f docker-compose.dev.yml exec postgres psql -U loop_hole -d loop_hole -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;"

# 3. 检查Redis性能
docker-compose -f docker-compose.dev.yml exec redis redis-cli --latency-history

# 4. 检查网络延迟
docker-compose -f docker-compose.dev.yml exec web ping -c 5 postgres
```

**解决方案**:
```bash
# 1. 优化数据库配置
# 在docker-compose.yml中添加：
environment:
  - POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements
  - POSTGRES_MAX_CONNECTIONS=200
  - POSTGRES_SHARED_BUFFERS=256MB

# 2. 调整Celery并发数
environment:
  - CELERY_WORKER_CONCURRENCY=2  # 减少并发数

# 3. 启用缓存
{
  "cache_config": {
    "enable": true,
    "ttl": 3600,
    "max_size": 1000
  }
}
```

### 问题2: 内存使用过高

**现象**:
```bash
docker stats
# 显示内存使用 > 80%
```

**解决方案**:
```bash
# 1. 限制容器内存使用
# 在docker-compose.yml中添加：
deploy:
  resources:
    limits:
      memory: 512M

# 2. 优化Python内存使用
environment:
  - PYTHONOPTIMIZE=1
  - PYTHONDONTWRITEBYTECODE=1

# 3. 清理缓存
docker-compose -f docker-compose.dev.yml exec redis redis-cli flushall
docker system prune -f
```

### 问题3: CPU使用过高

**解决方案**:
```bash
# 1. 调整工作进程数
environment:
  - CELERY_WORKER_CONCURRENCY=1

# 2. 添加任务间隔
{
  "schedule_config": {
    "interval": 60,  # 增加间隔时间
    "max_concurrent": 2  # 限制并发任务
  }
}

# 3. 优化代码
# 使用异步操作，避免阻塞操作
```

---

## 前端界面问题

### 问题1: 页面无法访问

**现象**: 浏览器显示 "无法访问此网站" 或 "ERR_CONNECTION_REFUSED"

**解决方案**:
```bash
# 1. 检查nginx容器状态
docker-compose -f docker-compose.dev.yml ps frontend

# 2. 检查端口映射
docker-compose -f docker-compose.dev.yml port frontend 80

# 3. 测试内部连接
docker-compose -f docker-compose.dev.yml exec frontend curl -I localhost:80

# 4. 重启前端服务
docker-compose -f docker-compose.dev.yml restart frontend
```

### 问题2: API请求失败

**现象**: 浏览器控制台显示 CORS 错误或 404 错误

**解决方案**:
```bash
# 1. 检查API配置
# 在前端环境变量中确认API地址
VITE_API_BASE_URL=http://localhost:8000

# 2. 检查CORS设置
# 在后端main.py中确认CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 3. 重新构建前端
docker-compose -f docker-compose.dev.yml build frontend
docker-compose -f docker-compose.dev.yml restart frontend
```

### 问题3: 前端显示空白页面

**诊断步骤**:
```bash
# 1. 检查浏览器控制台错误
# 按F12打开开发者工具，查看Console标签

# 2. 检查网络请求
# 在Network标签中查看请求状态

# 3. 检查前端日志
docker-compose -f docker-compose.dev.yml logs frontend
```

**解决方案**:
```bash
# 1. 清除浏览器缓存
# Ctrl+Shift+Delete 或 Cmd+Shift+Delete

# 2. 重新构建前端
docker-compose -f docker-compose.dev.yml build --no-cache frontend

# 3. 检查前端构建
docker-compose -f docker-compose.dev.yml exec frontend ls -la /usr/share/nginx/html/
```

---

## API接口问题

### 问题1: 500 内部服务器错误

**诊断步骤**:
```bash
# 1. 查看API日志
docker-compose -f docker-compose.dev.yml logs web

# 2. 测试API端点
curl -X GET "http://localhost:8000/api/v1/health" -H "accept: application/json"

# 3. 检查数据库连接
docker-compose -f docker-compose.dev.yml exec web python -c "
from app.database import engine
try:
    connection = engine.connect()
    print('Database connection: OK')
    connection.close()
except Exception as e:
    print(f'Database connection error: {e}')
"
```

### 问题2: 认证失败

**现象**:
```json
{
  "detail": "Could not validate credentials"
}
```

**解决方案**:
```bash
# 1. 检查JWT配置
docker-compose -f docker-compose.dev.yml exec web python -c "
from app.core.security import SECRET_KEY
print(f'Secret key: {SECRET_KEY[:10]}...')
"

# 2. 重新生成token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'

# 3. 检查token格式
# token应该以 "Bearer " 开头
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 问题3: 接口响应超时

**解决方案**:
```python
# 1. 增加超时时间
# 在nginx.conf中：
proxy_read_timeout 300;
proxy_connect_timeout 300;
proxy_send_timeout 300;

# 2. 优化查询
# 添加数据库索引
CREATE INDEX idx_extraction_results_task_id ON extraction_results(task_id);
CREATE INDEX idx_extraction_results_created_at ON extraction_results(created_at);

# 3. 使用分页
GET /api/v1/results?page=1&size=20
```

---

## Docker相关问题

### 问题1: 镜像构建失败

**现象**:
```bash
ERROR [build 5/8] RUN pip install -r requirements.txt
```

**解决方案**:
```bash
# 1. 清理Docker缓存
docker system prune -a -f
docker builder prune -a -f

# 2. 重新构建
docker-compose -f docker-compose.dev.yml build --no-cache

# 3. 检查网络连接
docker run --rm alpine ping -c 3 pypi.org

# 4. 使用国内镜像
# 在Dockerfile中添加：
RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题2: 容器启动失败

**诊断步骤**:
```bash
# 1. 查看容器日志
docker logs <container_id>

# 2. 检查容器配置
docker inspect <container_id>

# 3. 尝试手动运行
docker run -it <image_name> /bin/bash
```

### 问题3: 数据卷问题

**现象**: 数据丢失或无法持久化

**解决方案**:
```bash
# 1. 检查数据卷
docker volume ls
docker volume inspect loop_hole_postgres_data

# 2. 备份数据卷
docker run --rm -v loop_hole_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .

# 3. 恢复数据卷
docker run --rm -v loop_hole_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data
```

---

## 数据库问题

### 问题1: 数据库迁移失败

**现象**:
```bash
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.DuplicateTable) relation already exists
```

**解决方案**:
```bash
# 1. 检查迁移状态
docker-compose -f docker-compose.dev.yml exec web alembic current

# 2. 标记当前版本
docker-compose -f docker-compose.dev.yml exec web alembic stamp head

# 3. 创建新迁移
docker-compose -f docker-compose.dev.yml exec web alembic revision --autogenerate -m "fix migration"

# 4. 重置数据库（谨慎操作）
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d
```

### 问题2: 数据库连接池耗尽

**现象**:
```bash
sqlalchemy.exc.TimeoutError: QueuePool limit of size 5 overflow 10 reached
```

**解决方案**:
```python
# 在数据库配置中增加连接池大小
SQLALCHEMY_DATABASE_URL = "postgresql://..."
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600
)
```

### 问题3: 查询性能慢

**诊断查询**:
```sql
-- 查看慢查询
SELECT query, mean_exec_time, calls, total_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY mean_exec_time DESC;

-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

**优化方案**:
```sql
-- 添加索引
CREATE INDEX CONCURRENTLY idx_results_created_at ON extraction_results(created_at);
CREATE INDEX CONCURRENTLY idx_tasks_status ON extraction_tasks(status);

-- 定期清理旧数据
DELETE FROM extraction_results WHERE created_at < NOW() - INTERVAL '30 days';

-- 分析表统计信息
ANALYZE;
```

---

## 日志分析

### 关键日志位置

```bash
# Docker环境日志
docker-compose -f docker-compose.dev.yml logs web      # API日志
docker-compose -f docker-compose.dev.yml logs worker   # 任务执行日志
docker-compose -f docker-compose.dev.yml logs postgres # 数据库日志
docker-compose -f docker-compose.dev.yml logs redis    # 缓存日志

# 容器内日志路径
/app/logs/app.log          # 应用日志
/app/logs/celery.log       # Celery日志
/app/logs/extraction.log   # 数据提取日志
```

### 日志级别说明

- **ERROR**: 系统错误，需要立即处理
- **WARNING**: 警告信息，需要关注
- **INFO**: 一般信息，正常操作记录
- **DEBUG**: 调试信息，详细执行过程

### 常见错误模式

```bash
# 数据库连接错误
grep "OperationalError" logs/app.log

# Redis连接错误
grep "ConnectionError" logs/celery.log

# 页面加载超时
grep "TimeoutError" logs/extraction.log

# 内存不足
grep "MemoryError\|OutOfMemory" logs/app.log
```

---

## 系统监控

### 性能监控脚本

```bash
#!/bin/bash
# 保存为 monitor.sh

echo "=== Loop Hole 系统监控 ==="

while true; do
    clear
    echo "时间: $(date)"
    echo
    
    # 容器资源使用
    echo "容器资源使用:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}"
    
    echo
    echo "数据库连接数:"
    docker-compose -f docker-compose.dev.yml exec postgres psql -U loop_hole -d loop_hole -c "
    SELECT count(*) as connections, state 
    FROM pg_stat_activity 
    GROUP BY state;" 2>/dev/null
    
    echo
    echo "Redis内存使用:"
    docker-compose -f docker-compose.dev.yml exec redis redis-cli info memory | grep used_memory_human 2>/dev/null
    
    echo
    echo "任务队列状态:"
    docker-compose -f docker-compose.dev.yml exec redis redis-cli llen celery 2>/dev/null
    
    sleep 10
done
```

### 告警脚本

```bash
#!/bin/bash
# 保存为 alert.sh

# 设置阈值
CPU_THRESHOLD=80
MEMORY_THRESHOLD=80
DISK_THRESHOLD=85

# 检查CPU使用率
cpu_usage=$(docker stats --no-stream --format "{{.CPUPerc}}" | sed 's/%//' | awk '{sum+=$1} END {print int(sum/NR)}')
if [ "$cpu_usage" -gt "$CPU_THRESHOLD" ]; then
    echo "ALERT: CPU使用率过高: ${cpu_usage}%"
fi

# 检查内存使用率
memory_usage=$(docker stats --no-stream --format "{{.MemPerc}}" | sed 's/%//' | awk '{sum+=$1} END {print int(sum/NR)}')
if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
    echo "ALERT: 内存使用率过高: ${memory_usage}%"
fi

# 检查磁盘使用率
disk_usage=$(df / | awk 'NR==2 {print int($5)}')
if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
    echo "ALERT: 磁盘使用率过高: ${disk_usage}%"
fi
```

---

## 紧急恢复

### 系统完全重置

```bash
#!/bin/bash
# 紧急情况下完全重置系统

echo "警告: 此操作将删除所有数据！"
echo "按 Ctrl+C 取消，或等待10秒继续..."
sleep 10

# 停止所有服务
docker-compose -f docker-compose.dev.yml down -v

# 删除所有相关容器和镜像
docker system prune -a -f

# 删除数据卷
docker volume prune -f

# 重新启动
./quick_start.sh

echo "系统重置完成!"
```

### 数据备份和恢复

```bash
# 备份数据库
docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U loop_hole loop_hole > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker-compose -f docker-compose.dev.yml exec postgres psql -U loop_hole loop_hole < backup_20250115_120000.sql

# 备份配置文件
tar czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz .env docker-compose*.yml