# Loop Hole - 技术架构设计

## 系统架构概览

Loop Hole 采用微服务架构，包含以下核心组件：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Gateway   │    │   Admin Panel   │
│   (React)       │    │   (FastAPI)     │    │   (Vue.js)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Page Analyzer  │    │  Data Extractor │    │  Task Scheduler │
│   (Python)      │    │   (Python)      │    │   (Celery)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SQLite/PgSQL  │    │     Redis       │    │   File Storage  │
│   (Metadata)    │    │   (Cache/Queue) │    │   (MinIO/S3)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件设计

### 1. API Gateway (FastAPI)
**职责**：统一入口，路由分发，认证授权

**技术栈**：
- FastAPI + Uvicorn
- JWT认证
- Rate Limiting
- Request/Response日志

**核心接口**：
```python
# 任务管理
POST /api/v1/tasks/create
GET /api/v1/tasks/{task_id}
PUT /api/v1/tasks/{task_id}/start
DELETE /api/v1/tasks/{task_id}

# 数据提取
POST /api/v1/extract/analyze
POST /api/v1/extract/execute
GET /api/v1/extract/results/{result_id}

# 配置管理
GET /api/v1/configs/templates
POST /api/v1/configs/templates
PUT /api/v1/configs/templates/{template_id}
```

### 2. Page Analyzer (页面分析器)
**职责**：智能分析页面结构，生成数据提取规则

**核心算法**：
- **DOM树分析**：解析HTML结构，识别数据容器
- **模式识别**：使用机器学习识别数据展示模式
- **选择器生成**：自动生成CSS/XPath选择器

**技术实现**：
```python
class PageAnalyzer:
    def __init__(self):
        self.dom_parser = BeautifulSoup
        self.ml_model = load_pattern_recognition_model()
        self.selector_generator = SelectorGenerator()
    
    def analyze_page(self, html_content: str) -> AnalysisResult:
        # 1. 解析DOM结构
        dom = self.dom_parser(html_content, 'html.parser')
        
        # 2. 识别数据容器
        containers = self.identify_data_containers(dom)
        
        # 3. 生成提取规则
        rules = self.generate_extraction_rules(containers)
        
        return AnalysisResult(containers=containers, rules=rules)
```

### 3. Data Extractor (数据提取器)
**职责**：基于分析结果执行数据提取

**核心功能**：
- **浏览器自动化**：使用Selenium/Playwright
- **数据清洗**：标准化数据格式
- **增量提取**：只提取变化的数据
- **并发处理**：支持多页面并行提取

**技术实现**：
```python
class DataExtractor:
    def __init__(self):
        self.browser_pool = BrowserPool()
        self.data_cleaner = DataCleaner()
        self.cache_manager = CacheManager()
    
    async def extract_data(self, task: ExtractionTask) -> ExtractionResult:
        browser = await self.browser_pool.get_browser()
        try:
            # 1. 加载页面
            await browser.goto(task.url)
            
            # 2. 等待动态内容加载
            await self.wait_for_content(browser, task.wait_conditions)
            
            # 3. 执行数据提取
            raw_data = await self.execute_extraction_rules(browser, task.rules)
            
            # 4. 数据清洗和验证
            cleaned_data = self.data_cleaner.clean(raw_data)
            
            return ExtractionResult(data=cleaned_data, metadata=task.metadata)
        finally:
            await self.browser_pool.return_browser(browser)
```

### 4. Task Scheduler (任务调度器)
**职责**：管理和调度数据提取任务

**技术栈**：
- Celery + Redis
- Cron表达式支持
- 任务队列管理
- 失败重试机制

**任务类型**：
```python
# 定时任务
@celery_app.task(bind=True, max_retries=3)
def scheduled_extraction_task(self, task_config):
    try:
        result = extract_data_from_page(task_config)
        save_extraction_result(result)
        return result
    except Exception as exc:
        self.retry(countdown=60, exc=exc)

# 触发式任务
@celery_app.task
def triggered_extraction_task(page_url, extraction_rules):
    return extract_data_from_page(page_url, extraction_rules)
```

## 数据存储设计

### 1. 关系数据库 (元数据存储)
**支持数据库**：SQLite (开发/小规模) / PostgreSQL (生产/大规模)

**表结构设计**：

```sql
-- 任务配置表
CREATE TABLE extraction_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    extraction_rules JSONB NOT NULL,
    schedule_config JSONB,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 提取结果表
CREATE TABLE extraction_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES extraction_tasks(id),
    data JSONB NOT NULL,
    metadata JSONB,
    extracted_at TIMESTAMP DEFAULT NOW(),
    data_hash VARCHAR(64) -- 用于增量检测
);

-- 页面分析缓存表
CREATE TABLE page_analysis_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url_hash VARCHAR(64) UNIQUE,
    analysis_result JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);
```

### 2. Redis (缓存和队列)
**用途**：
- 任务队列存储
- 页面分析结果缓存
- 会话状态管理
- 实时数据缓存

**数据结构**：
```python
# 任务队列
LPUSH extraction_queue task_id
BRPOP extraction_queue 0

# 分析结果缓存
SETEX page_analysis:{url_hash} 3600 analysis_result_json

# 实时数据缓存
HSET extraction_results:{task_id} data result_json
HSET extraction_results:{task_id} timestamp unix_timestamp
```

### 3. 文件存储 (MinIO/S3)
**存储内容**：
- 页面截图
- 原始HTML文件
- 导出的数据文件
- 日志文件

## 安全设计

### 1. 认证授权
```python
# JWT Token认证
class JWTAuth:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def create_token(self, user_id: str, permissions: List[str]) -> str:
        payload = {
            'user_id': user_id,
            'permissions': permissions,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token: str) -> Dict:
        return jwt.decode(token, self.secret_key, algorithms=['HS256'])
```

### 2. 数据加密
- **传输加密**：HTTPS/TLS 1.3
- **存储加密**：数据库字段级加密
- **密钥管理**：使用环境变量或密钥管理服务

### 3. 访问控制
```python
# 基于角色的权限控制
class RoleBasedAccessControl:
    PERMISSIONS = {
        'admin': ['read', 'write', 'delete', 'manage'],
        'operator': ['read', 'write'],
        'viewer': ['read']
    }
    
    def check_permission(self, user_role: str, action: str) -> bool:
        return action in self.PERMISSIONS.get(user_role, [])
```

## 性能优化

### 1. 并发处理
- **异步编程**：使用asyncio处理I/O密集型操作
- **连接池**：数据库和浏览器连接池
- **任务并行**：多进程/多线程处理

### 2. 缓存策略
```python
# 多级缓存
class CacheManager:
    def __init__(self):
        self.l1_cache = {}  # 内存缓存
        self.l2_cache = redis_client  # Redis缓存
        self.l3_cache = database  # 数据库
    
    async def get(self, key: str):
        # L1缓存
        if key in self.l1_cache:
            return self.l1_cache[key]
        
        # L2缓存
        value = await self.l2_cache.get(key)
        if value:
            self.l1_cache[key] = value
            return value
        
        # L3缓存
        value = await self.l3_cache.get(key)
        if value:
            await self.l2_cache.setex(key, 3600, value)
            self.l1_cache[key] = value
        
        return value
```

### 3. 数据库优化

**SQLite优化**：
- **WAL模式**：启用Write-Ahead Logging提升并发性能
- **索引优化**：为查询字段创建合适索引
- **PRAGMA优化**：调整cache_size、synchronous等参数

**PostgreSQL优化**：
- **索引优化**：为查询字段创建合适索引，使用JSONB索引
- **分区表**：按时间分区存储历史数据
- **读写分离**：使用主从复制分离读写操作
- **连接池**：使用pgbouncer等连接池工具

## 监控和日志

### 1. 应用监控
```python
# Prometheus指标收集
from prometheus_client import Counter, Histogram, Gauge

extraction_counter = Counter('extractions_total', 'Total extractions')
extraction_duration = Histogram('extraction_duration_seconds', 'Extraction duration')
active_tasks = Gauge('active_tasks', 'Number of active tasks')
```

### 2. 日志系统
```python
# 结构化日志
import structlog

logger = structlog.get_logger()

def log_extraction_start(task_id: str, url: str):
    logger.info("extraction_started", task_id=task_id, url=url)

def log_extraction_complete(task_id: str, duration: float, data_count: int):
    logger.info("extraction_completed", 
                task_id=task_id, 
                duration=duration, 
                data_count=data_count)
```

## 部署架构

### 1. 容器化部署
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Docker Compose
```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/loophole
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  worker:
    build: .
    command: celery -A app.celery worker --loglevel=info
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=loophole
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 3. Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: loophole-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: loophole-api
  template:
    metadata:
      labels:
        app: loophole-api
    spec:
      containers:
      - name: api
        image: loophole:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: loophole-secrets
              key: database-url
```

## 扩展性设计

### 1. 插件架构
```python
# 插件接口
class DataProcessorPlugin:
    def process(self, data: Dict) -> Dict:
        raise NotImplementedError

# 插件注册
class PluginManager:
    def __init__(self):
        self.plugins = {}
    
    def register_plugin(self, name: str, plugin: DataProcessorPlugin):
        self.plugins[name] = plugin
    
    def apply_plugins(self, data: Dict) -> Dict:
        for plugin in self.plugins.values():
            data = plugin.process(data)
        return data
```

### 2. 水平扩展
- **无状态设计**：所有服务都是无状态的
- **负载均衡**：使用Nginx或云负载均衡器
- **数据分片**：按租户或时间分片存储数据
