-- 初始化数据库脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS loop_hole;

-- 使用数据库
\c loop_hole;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建提取任务表
CREATE TABLE IF NOT EXISTS extraction_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    url TEXT NOT NULL,
    extraction_rules JSONB NOT NULL,
    schedule_config JSONB,
    auth_config JSONB,
    status VARCHAR(20) DEFAULT 'active',
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_run TIMESTAMP,
    next_run TIMESTAMP,
    last_success TIMESTAMP,
    total_runs INTEGER DEFAULT 0,
    error_message TEXT
);

-- 创建提取结果表
CREATE TABLE IF NOT EXISTS extraction_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES extraction_tasks(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'success',
    extracted_data JSONB,
    metadata JSONB,
    record_count INTEGER DEFAULT 0,
    file_path TEXT,
    file_size BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建页面分析缓存表
CREATE TABLE IF NOT EXISTS page_analysis_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url_hash VARCHAR(64) NOT NULL,
    html_hash VARCHAR(64) NOT NULL,
    analysis_result JSONB NOT NULL,
    confidence_score FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_extraction_tasks_status ON extraction_tasks(status);
CREATE INDEX IF NOT EXISTS idx_extraction_tasks_created_by ON extraction_tasks(created_by);
CREATE INDEX IF NOT EXISTS idx_extraction_tasks_next_run ON extraction_tasks(next_run);
CREATE INDEX IF NOT EXISTS idx_extraction_results_task_id ON extraction_results(task_id);
CREATE INDEX IF NOT EXISTS idx_extraction_results_created_at ON extraction_results(created_at);
CREATE INDEX IF NOT EXISTS idx_page_analysis_cache_url_hash ON page_analysis_cache(url_hash);
CREATE INDEX IF NOT EXISTS idx_page_analysis_cache_expires_at ON page_analysis_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 插入默认管理员用户
INSERT INTO users (username, email, hashed_password, role) 
VALUES ('admin', '<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'admin')
ON CONFLICT (username) DO NOTHING;

-- 创建触发器函数来自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 extraction_tasks 表创建触发器
CREATE TRIGGER update_extraction_tasks_updated_at 
    BEFORE UPDATE ON extraction_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
